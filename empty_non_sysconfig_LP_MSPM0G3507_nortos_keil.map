Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to timer.o(.text.Timer_Start) for Timer_Start
    main.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to user_ir_sensor.o(.text.ADC_Init) for ADC_Init
    main.o(.text.main) refers to user_ir_sensor.o(.text.ADC_start) for ADC_start
    main.o(.text.main) refers to oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) for OLEDLCD_Refresh_AllGDRAM
    main.o(.text.main) refers to delay.o(.text.delay_ms) for delay_ms
    main.o(.text.main) refers to user_ir_sensor.o(.text.Display_ADC) for Display_ADC
    main.o(.text.main) refers to user_ir_sensor.o(.text.Binarization_Display) for Binarization_Display
    main.o(.text.main) refers to counting.o(.text.Display_Counting_Info) for Display_Counting_Info
    main.o(.text.main) refers to key.o(.bss.KEYR_Flag) for KEYR_Flag
    main.o(.text.main) refers to main.o(.data.Mode) for Mode
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.TIMG12_IRQHandler) refers to user_control.o(.text.Control) for Control
    main.o(.text.TIMG12_IRQHandler) refers to key.o(.text.Check_Key) for Check_Key
    main.o(.text.TIMG12_IRQHandler) refers to counting.o(.text.counting) for counting
    main.o(.text.TIMG12_IRQHandler) refers to counting.o(.text.keycontrol) for keycontrol
    main.o(.text.TIMG12_IRQHandler) refers to user_ir_sensor.o(.bss.ADC_Flag) for ADC_Flag
    main.o(.text.TIMG12_IRQHandler) refers to user_ir_sensor.o(.bss.ADC1_Flag) for ADC1_Flag
    main.o(.text.TIMG12_IRQHandler) refers to main.o(.bss.LED_Flag) for LED_Flag
    main.o(.ARM.exidx.text.TIMG12_IRQHandler) refers to main.o(.text.TIMG12_IRQHandler) for [Anonymous Symbol]
    user_ir_sensor.o(.text.ADC0_IRQHandler) refers to user_ir_sensor.o(.bss.ADC_Data) for ADC_Data
    user_ir_sensor.o(.text.ADC0_IRQHandler) refers to user_ir_sensor.o(.bss.ADC_Flag) for ADC_Flag
    user_ir_sensor.o(.ARM.exidx.text.ADC0_IRQHandler) refers to user_ir_sensor.o(.text.ADC0_IRQHandler) for [Anonymous Symbol]
    user_ir_sensor.o(.text.ADC1_IRQHandler) refers to user_ir_sensor.o(.bss.ADC_Data) for ADC_Data
    user_ir_sensor.o(.text.ADC1_IRQHandler) refers to user_ir_sensor.o(.bss.ADC1_Flag) for ADC1_Flag
    user_ir_sensor.o(.ARM.exidx.text.ADC1_IRQHandler) refers to user_ir_sensor.o(.text.ADC1_IRQHandler) for [Anonymous Symbol]
    user_ir_sensor.o(.ARM.exidx.text.ADC_Init) refers to user_ir_sensor.o(.text.ADC_Init) for [Anonymous Symbol]
    user_ir_sensor.o(.ARM.exidx.text.ADC_start) refers to user_ir_sensor.o(.text.ADC_start) for [Anonymous Symbol]
    user_ir_sensor.o(.text.Display_ADC) refers to oled_user.o(.text.OLEDLCD_Put6x7Num) for OLEDLCD_Put6x7Num
    user_ir_sensor.o(.text.Display_ADC) refers to user_ir_sensor.o(.bss.ADC_Data) for ADC_Data
    user_ir_sensor.o(.ARM.exidx.text.Display_ADC) refers to user_ir_sensor.o(.text.Display_ADC) for [Anonymous Symbol]
    user_ir_sensor.o(.text.Binarization_Display) refers to oled_user.o(.text.OLEDLCD_Put6x7ENstr) for OLEDLCD_Put6x7ENstr
    user_ir_sensor.o(.text.Binarization_Display) refers to oled_user.o(.text.OLEDLCD_Put6x12Num) for OLEDLCD_Put6x12Num
    user_ir_sensor.o(.text.Binarization_Display) refers to oled_user.o(.text.OLEDLCD_Put6x7Num) for OLEDLCD_Put6x7Num
    user_ir_sensor.o(.text.Binarization_Display) refers to user_ir_sensor.o(.bss.IR_RES) for IR_RES
    user_ir_sensor.o(.text.Binarization_Display) refers to user_ir_sensor.o(.rodata.str1.1) for [Anonymous Symbol]
    user_ir_sensor.o(.text.Binarization_Display) refers to user_ir_sensor.o(.bss.Error) for Error
    user_ir_sensor.o(.text.Binarization_Display) refers to user_ir_sensor.o(.bss.IR_RES_Original) for IR_RES_Original
    user_ir_sensor.o(.text.Binarization_Display) refers to user_ir_sensor.o(.bss.IR_RES_For_Error) for IR_RES_For_Error
    user_ir_sensor.o(.ARM.exidx.text.Binarization_Display) refers to user_ir_sensor.o(.text.Binarization_Display) for [Anonymous Symbol]
    user_ir_sensor.o(.text.Display_Threshold) refers to oled_user.o(.text.OLEDLCD_Put6x7Num) for OLEDLCD_Put6x7Num
    user_ir_sensor.o(.text.Display_Threshold) refers to user_ir_sensor.o(.data.ADC_Threshold) for ADC_Threshold
    user_ir_sensor.o(.ARM.exidx.text.Display_Threshold) refers to user_ir_sensor.o(.text.Display_Threshold) for [Anonymous Symbol]
    user_ir_sensor.o(.text.ADC_Transform) refers to user_ir_sensor.o(.bss.IR_RES) for IR_RES
    user_ir_sensor.o(.text.ADC_Transform) refers to user_ir_sensor.o(.bss.ADC_Data) for ADC_Data
    user_ir_sensor.o(.text.ADC_Transform) refers to user_ir_sensor.o(.data.ADC_Threshold) for ADC_Threshold
    user_ir_sensor.o(.text.ADC_Transform) refers to user_ir_sensor.o(.bss.IR_RES_For_Error) for IR_RES_For_Error
    user_ir_sensor.o(.text.ADC_Transform) refers to user_ir_sensor.o(.bss.IR_RES_Original) for IR_RES_Original
    user_ir_sensor.o(.text.ADC_Transform) refers to user_ir_sensor.o(.bss.Error) for Error
    user_ir_sensor.o(.ARM.exidx.text.ADC_Transform) refers to user_ir_sensor.o(.text.ADC_Transform) for [Anonymous Symbol]
    user_control.o(.text.Control) refers to user_ir_sensor.o(.text.ADC_Transform) for ADC_Transform
    user_control.o(.text.Control) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    user_control.o(.text.Control) refers to motor.o(.text.Set_Motor) for Set_Motor
    user_control.o(.text.Control) refers to key.o(.text.Check_Key) for Check_Key
    user_control.o(.text.Control) refers to user_control.o(.bss.SysTime_Stick) for SysTime_Stick
    user_control.o(.text.Control) refers to user_ir_sensor.o(.bss.IR_RES) for IR_RES
    user_control.o(.text.Control) refers to user_control.o(.data.Line_Out_timesStik) for Line_Out_timesStik
    user_control.o(.text.Control) refers to user_control.o(.bss.Line_Out_Flag) for Line_Out_Flag
    user_control.o(.text.Control) refers to main.o(.data.Mode) for Mode
    user_control.o(.text.Control) refers to user_parameter.o(.bss.Last_Error) for Last_Error
    user_control.o(.text.Control) refers to user_ir_sensor.o(.bss.Error) for Error
    user_control.o(.text.Control) refers to user_parameter.o(.bss.Sum_Error) for Sum_Error
    user_control.o(.text.Control) refers to user_parameter.o(.data.PID_P) for PID_P
    user_control.o(.text.Control) refers to user_parameter.o(.bss.PID_I) for PID_I
    user_control.o(.text.Control) refers to user_parameter.o(.bss.PID_D) for PID_D
    user_control.o(.text.Control) refers to user_parameter.o(.bss.PID_Out) for PID_Out
    user_control.o(.text.Control) refers to user_parameter.o(.data.Speed) for Speed
    user_control.o(.ARM.exidx.text.Control) refers to user_control.o(.text.Control) for [Anonymous Symbol]
    user_control.o(.text.IR_Identification) refers to user_ir_sensor.o(.bss.IR_RES) for IR_RES
    user_control.o(.text.IR_Identification) refers to user_control.o(.data.Line_Out_timesStik) for Line_Out_timesStik
    user_control.o(.text.IR_Identification) refers to user_control.o(.bss.Line_Out_Flag) for Line_Out_Flag
    user_control.o(.ARM.exidx.text.IR_Identification) refers to user_control.o(.text.IR_Identification) for [Anonymous Symbol]
    user_parameter.o(.ARM.exidx.text.Write_Parameter) refers to user_parameter.o(.text.Write_Parameter) for [Anonymous Symbol]
    user_parameter.o(.ARM.exidx.text.Read_Parameter) refers to user_parameter.o(.text.Read_Parameter) for [Anonymous Symbol]
    counting.o(.text.counting) refers to counting.o(.data.Target_Laps) for Target_Laps
    counting.o(.text.counting) refers to counting.o(.bss.total_time_limit) for [Anonymous Symbol]
    counting.o(.text.counting) refers to counting.o(.bss.delay_counter) for [Anonymous Symbol]
    counting.o(.text.counting) refers to counting.o(.bss.lap_timer) for [Anonymous Symbol]
    counting.o(.text.counting) refers to user_ir_sensor.o(.bss.Error) for Error
    counting.o(.text.counting) refers to counting.o(.bss.i) for i
    counting.o(.text.counting) refers to counting.o(.bss.j) for j
    counting.o(.ARM.exidx.text.counting) refers to counting.o(.text.counting) for [Anonymous Symbol]
    counting.o(.text.keycontrol) refers to counting.o(.bss.key_delay_counter) for [Anonymous Symbol]
    counting.o(.text.keycontrol) refers to counting.o(.bss.key_pressed) for [Anonymous Symbol]
    counting.o(.text.keycontrol) refers to counting.o(.data.Target_Laps) for Target_Laps
    counting.o(.ARM.exidx.text.keycontrol) refers to counting.o(.text.keycontrol) for [Anonymous Symbol]
    counting.o(.text.Display_Counting_Info) refers to oled_user.o(.text.OLEDLCD_Put6x7ENstr) for OLEDLCD_Put6x7ENstr
    counting.o(.text.Display_Counting_Info) refers to oled_user.o(.text.OLEDLCD_Put6x7Num) for OLEDLCD_Put6x7Num
    counting.o(.text.Display_Counting_Info) refers to counting.o(.bss.j) for j
    counting.o(.text.Display_Counting_Info) refers to counting.o(.bss.i) for i
    counting.o(.text.Display_Counting_Info) refers to counting.o(.data.Target_Laps) for Target_Laps
    counting.o(.ARM.exidx.text.Display_Counting_Info) refers to counting.o(.text.Display_Counting_Info) for [Anonymous Symbol]
    ui_main.o(.text.UI_Main_Initial) refers to oled_user.o(.text.OLEDLCD_ClearBuffer) for OLEDLCD_ClearBuffer
    ui_main.o(.text.UI_Main_Initial) refers to oled_user.o(.text.OLEDLCD_Put6x7ENstr) for OLEDLCD_Put6x7ENstr
    ui_main.o(.text.UI_Main_Initial) refers to oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) for OLEDLCD_Refresh_AllGDRAM
    ui_main.o(.text.UI_Main_Initial) refers to main.o(.data.Mode) for Mode
    ui_main.o(.ARM.exidx.text.UI_Main_Initial) refers to ui_main.o(.text.UI_Main_Initial) for [Anonymous Symbol]
    ui_main.o(.text.UI_Main_Operation) refers to user_ir_sensor.o(.text.Display_ADC) for Display_ADC
    ui_main.o(.text.UI_Main_Operation) refers to user_ir_sensor.o(.text.ADC_Transform) for ADC_Transform
    ui_main.o(.text.UI_Main_Operation) refers to oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) for OLEDLCD_Refresh_AllGDRAM
    ui_main.o(.text.UI_Main_Operation) refers to key.o(.bss.KEYM_Flag) for KEYM_Flag
    ui_main.o(.text.UI_Main_Operation) refers to main.o(.data.Mode) for Mode
    ui_main.o(.ARM.exidx.text.UI_Main_Operation) refers to ui_main.o(.text.UI_Main_Operation) for [Anonymous Symbol]
    ui_menu.o(.text.UI_Menu_Initial) refers to oled_user.o(.text.OLEDLCD_ClearBuffer) for OLEDLCD_ClearBuffer
    ui_menu.o(.text.UI_Menu_Initial) refers to oled_user.o(.text.OLEDLCD_Put12x12CNstr) for OLEDLCD_Put12x12CNstr
    ui_menu.o(.text.UI_Menu_Initial) refers to oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) for OLEDLCD_Refresh_AllGDRAM
    ui_menu.o(.text.UI_Menu_Initial) refers to ui_menu.o(.rodata.str1.1) for [Anonymous Symbol]
    ui_menu.o(.text.UI_Menu_Initial) refers to main.o(.data.Mode) for Mode
    ui_menu.o(.text.UI_Menu_Initial) refers to ui_menu.o(.bss.Menu_x) for Menu_x
    ui_menu.o(.ARM.exidx.text.UI_Menu_Initial) refers to ui_menu.o(.text.UI_Menu_Initial) for [Anonymous Symbol]
    ui_menu.o(.text.UI_Menu_Operation) refers to oled_user.o(.text.OLEDLCD_Put12x12CNstr) for OLEDLCD_Put12x12CNstr
    ui_menu.o(.text.UI_Menu_Operation) refers to oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) for OLEDLCD_Refresh_AllGDRAM
    ui_menu.o(.text.UI_Menu_Operation) refers to key.o(.bss.KEYM_Flag) for KEYM_Flag
    ui_menu.o(.text.UI_Menu_Operation) refers to ui_menu.o(.bss.Menu_x) for Menu_x
    ui_menu.o(.text.UI_Menu_Operation) refers to main.o(.data.Mode) for Mode
    ui_menu.o(.text.UI_Menu_Operation) refers to key.o(.bss.KEYU_Flag) for KEYU_Flag
    ui_menu.o(.text.UI_Menu_Operation) refers to ui_menu.o(.rodata.str1.1) for [Anonymous Symbol]
    ui_menu.o(.text.UI_Menu_Operation) refers to key.o(.bss.KEYD_Flag) for KEYD_Flag
    ui_menu.o(.ARM.exidx.text.UI_Menu_Operation) refers to ui_menu.o(.text.UI_Menu_Operation) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to user_ir_sensor.o(.text.ADC0_IRQHandler) for ADC0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to user_ir_sensor.o(.text.ADC1_IRQHandler) for ADC1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to main.o(.text.TIMG12_IRQHandler) for TIMG12_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for SYSCFG_DL_MOTOR_PWM_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) for SYSCFG_DL_SERVO1_PWM_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) for SYSCFG_DL_SERVO2_PWM_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) for SYSCFG_DL_QEI_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init) for SYSCFG_DL_CAPTURE_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init) for SYSCFG_DL_SYS_TIMER_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init) for SYSCFG_DL_HC05_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init) for SYSCFG_DL_SENSER_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init) for SYSCFG_DL_SERVO_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init) for SYSCFG_DL_LCD_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) for SYSCFG_DL_ADC12_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init) for SYSCFG_DL_ADC12_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Cross_Trigger_init) for SYSCFG_DL_PWM_Cross_Trigger_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gSERVO2_PWMBackup) for gSERVO2_PWMBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWMBackup) for gMOTOR_PWMBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gQEI_0Backup) for gQEI_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gCAPTURE_0Backup) for gCAPTURE_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gLCD_SPIBackup) for gLCD_SPIBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for DL_Timer_getCaptureCompareCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for DL_Timer_getCaptureCompareCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to ti_msp_dl_config.o(.rodata.gSERVO1_PWMClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) refers to ti_msp_dl_config.o(.rodata.gSERVO1_PWMConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SERVO1_PWM_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) refers to ti_msp_dl_config.o(.rodata.gSERVO2_PWMClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) refers to ti_msp_dl_config.o(.rodata.gSERVO2_PWMConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SERVO2_PWM_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) refers to ti_msp_dl_config.o(.rodata.gQEI_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_QEI_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for DL_Timer_initCaptureMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init) refers to ti_msp_dl_config.o(.rodata.gCAPTURE_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init) refers to ti_msp_dl_config.o(.rodata.gCAPTURE_0CaptureConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_CAPTURE_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init) refers to ti_msp_dl_config.o(.rodata.gSYS_TIMERClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init) refers to ti_msp_dl_config.o(.rodata.gSYS_TIMERTimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYS_TIMER_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init) refers to ti_msp_dl_config.o(.rodata.gHC05_UARTClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init) refers to ti_msp_dl_config.o(.rodata.gHC05_UARTConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_HC05_UART_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init) refers to ti_msp_dl_config.o(.rodata.gSENSER_UARTClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init) refers to ti_msp_dl_config.o(.rodata.gSENSER_UARTConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SENSER_UART_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init) refers to ti_msp_dl_config.o(.rodata.gSERVO_UARTClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init) refers to ti_msp_dl_config.o(.rodata.gSERVO_UARTConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SERVO_UART_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init) refers to ti_msp_dl_config.o(.rodata.gLCD_SPI_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init) refers to ti_msp_dl_config.o(.rodata.gLCD_SPI_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_LCD_SPI_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) refers to ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init) refers to ti_msp_dl_config.o(.rodata.gADC12_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Cross_Trigger_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Cross_Trigger_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWMBackup) for gMOTOR_PWMBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gSERVO2_PWMBackup) for gSERVO2_PWMBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gQEI_0Backup) for gQEI_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gCAPTURE_0Backup) for gCAPTURE_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gLCD_SPIBackup) for gLCD_SPIBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWMBackup) for gMOTOR_PWMBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gSERVO2_PWMBackup) for gSERVO2_PWMBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gQEI_0Backup) for gQEI_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gCAPTURE_0Backup) for gCAPTURE_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gLCD_SPIBackup) for gLCD_SPIBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    delay.o(.text.delay_ms) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    delay.o(.ARM.exidx.text.delay_ms) refers to delay.o(.text.delay_ms) for [Anonymous Symbol]
    delay.o(.text.delay_us) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    delay.o(.ARM.exidx.text.delay_us) refers to delay.o(.text.delay_us) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.LED_Test) refers to led.o(.text.LED_Test) for [Anonymous Symbol]
    key.o(.text.Check_Key) refers to key.o(.bss.KEYU_Flag) for KEYU_Flag
    key.o(.text.Check_Key) refers to key.o(.bss.KEYU_Times) for KEYU_Times
    key.o(.text.Check_Key) refers to key.o(.bss.KEYD_Flag) for KEYD_Flag
    key.o(.text.Check_Key) refers to key.o(.bss.KEYD_Times) for KEYD_Times
    key.o(.text.Check_Key) refers to key.o(.bss.KEYR_Flag) for KEYR_Flag
    key.o(.text.Check_Key) refers to key.o(.bss.KEYR_Times) for KEYR_Times
    key.o(.text.Check_Key) refers to key.o(.bss.KEYL_Flag) for KEYL_Flag
    key.o(.text.Check_Key) refers to key.o(.bss.KEYL_Times) for KEYL_Times
    key.o(.text.Check_Key) refers to key.o(.bss.KEYM_Flag) for KEYM_Flag
    key.o(.text.Check_Key) refers to key.o(.bss.KEYM_Times) for KEYM_Times
    key.o(.ARM.exidx.text.Check_Key) refers to key.o(.text.Check_Key) for [Anonymous Symbol]
    uart.o(.text.UART_Test) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    uart.o(.ARM.exidx.text.UART_Test) refers to uart.o(.text.UART_Test) for [Anonymous Symbol]
    uart.o(.text.UART0_IRQHandler) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for DL_UART_receiveDataBlocking
    uart.o(.text.UART0_IRQHandler) refers to uart.o(.bss.rxData) for rxData
    uart.o(.ARM.exidx.text.UART0_IRQHandler) refers to uart.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    timer.o(.ARM.exidx.text.Timer_Start) refers to timer.o(.text.Timer_Start) for [Anonymous Symbol]
    timer.o(.text.Timer_PWM_Test) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    timer.o(.text.Timer_PWM_Test) refers to delay.o(.text.delay_ms) for delay_ms
    timer.o(.text.Timer_PWM_Test) refers to timer.o(.bss.PWMx) for PWMx
    timer.o(.ARM.exidx.text.Timer_PWM_Test) refers to timer.o(.text.Timer_PWM_Test) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to delay.o(.text.delay_ms) for delay_ms
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Fill) for OLED_Fill
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_WriteCmd) refers to oled.o(.text.OLED_WriteCmd) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_Fill) refers to oled.o(.text.OLED_Fill) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_WriteDat) refers to oled.o(.text.OLED_WriteDat) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) refers to oled.o(.text.OLED_WriteCmd) for OLED_WriteCmd
    oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) refers to oled.o(.text.OLED_WriteDat) for OLED_WriteDat
    oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) refers to oled_user.o(.bss.OLEDLCD_Buffer) for OLEDLCD_Buffer
    oled_user.o(.ARM.exidx.text.OLEDLCD_Refresh_AllGDRAM) refers to oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_ClearBuffer) refers to aeabi_memset4.o(.text) for __aeabi_memset4
    oled_user.o(.text.OLEDLCD_ClearBuffer) refers to oled_user.o(.bss.OLEDLCD_Buffer) for OLEDLCD_Buffer
    oled_user.o(.ARM.exidx.text.OLEDLCD_ClearBuffer) refers to oled_user.o(.text.OLEDLCD_ClearBuffer) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_DrawDot) refers to oled_user.o(.bss.OLEDLCD_Buffer) for OLEDLCD_Buffer
    oled_user.o(.ARM.exidx.text.OLEDLCD_DrawDot) refers to oled_user.o(.text.OLEDLCD_DrawDot) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put6x12Char) refers to oled_user.o(.rodata.ASCII6X12) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put6x12Char) refers to oled_user.o(.bss.OLEDLCD_Buffer) for OLEDLCD_Buffer
    oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x12Char) refers to oled_user.o(.text.OLEDLCD_Put6x12Char) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put6x7Char) refers to oled_user.o(.rodata.ASCII5x7) for ASCII5x7
    oled_user.o(.text.OLEDLCD_Put6x7Char) refers to oled_user.o(.bss.OLEDLCD_Buffer) for OLEDLCD_Buffer
    oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x7Char) refers to oled_user.o(.text.OLEDLCD_Put6x7Char) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put12x12CNstr) refers to oled_user.o(.rodata.ASCII6X12) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put12x12CNstr) refers to oled_user.o(.bss.OLEDLCD_Buffer) for OLEDLCD_Buffer
    oled_user.o(.text.OLEDLCD_Put12x12CNstr) refers to oled_user.o(.rodata.hanzi_12x12) for [Anonymous Symbol]
    oled_user.o(.ARM.exidx.text.OLEDLCD_Put12x12CNstr) refers to oled_user.o(.text.OLEDLCD_Put12x12CNstr) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put6x7ENstr) refers to oled_user.o(.text.OLEDLCD_Put6x7Char) for OLEDLCD_Put6x7Char
    oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x7ENstr) refers to oled_user.o(.text.OLEDLCD_Put6x7ENstr) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put6x12Num) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled_user.o(.text.OLEDLCD_Put6x12Num) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled_user.o(.text.OLEDLCD_Put6x12Num) refers to aeabi_memset.o(.text) for __aeabi_memset
    oled_user.o(.text.OLEDLCD_Put6x12Num) refers to oled_user.o(.text.OLEDLCD_Put12x12CNstr) for OLEDLCD_Put12x12CNstr
    oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x12Num) refers to oled_user.o(.text.OLEDLCD_Put6x12Num) for [Anonymous Symbol]
    oled_user.o(.text.OLEDLCD_Put6x7Num) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled_user.o(.text.OLEDLCD_Put6x7Num) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled_user.o(.text.OLEDLCD_Put6x7Num) refers to aeabi_memset.o(.text) for __aeabi_memset
    oled_user.o(.text.OLEDLCD_Put6x7Num) refers to oled_user.o(.text.OLEDLCD_Put6x7Char) for OLEDLCD_Put6x7Char
    oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x7Num) refers to oled_user.o(.text.OLEDLCD_Put6x7Num) for [Anonymous Symbol]
    motor.o(.text.Set_Motor) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Set_Motor) refers to motor.o(.text.Set_Motor) for [Anonymous Symbol]
    buzzer.o(.ARM.exidx.text.BEEP_test) refers to buzzer.o(.text.BEEP_test) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    aeabi_memset4.o(.text) refers to rt_memclr.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.TIMG12_IRQHandler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing user_ir_sensor.o(.text), (0 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.ADC0_IRQHandler), (8 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.ADC1_IRQHandler), (8 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.ADC_Init), (8 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.ADC_start), (8 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.Display_ADC), (8 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.Binarization_Display), (8 bytes).
    Removing user_ir_sensor.o(.text.Display_Threshold), (76 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.Display_Threshold), (8 bytes).
    Removing user_ir_sensor.o(.ARM.exidx.text.ADC_Transform), (8 bytes).
    Removing user_ir_sensor.o(.bss.FLAG_left), (4 bytes).
    Removing user_ir_sensor.o(.data.ADC_Min), (12 bytes).
    Removing user_ir_sensor.o(.data.ADC_Max), (12 bytes).
    Removing user_control.o(.text), (0 bytes).
    Removing user_control.o(.ARM.exidx.text.Control), (8 bytes).
    Removing user_control.o(.text.IR_Identification), (56 bytes).
    Removing user_control.o(.ARM.exidx.text.IR_Identification), (8 bytes).
    Removing user_parameter.o(.text), (0 bytes).
    Removing user_parameter.o(.text.Write_Parameter), (2 bytes).
    Removing user_parameter.o(.ARM.exidx.text.Write_Parameter), (8 bytes).
    Removing user_parameter.o(.text.Read_Parameter), (2 bytes).
    Removing user_parameter.o(.ARM.exidx.text.Read_Parameter), (8 bytes).
    Removing counting.o(.text), (0 bytes).
    Removing counting.o(.ARM.exidx.text.counting), (8 bytes).
    Removing counting.o(.ARM.exidx.text.keycontrol), (8 bytes).
    Removing counting.o(.ARM.exidx.text.Display_Counting_Info), (8 bytes).
    Removing counting.o(.bss.flag), (4 bytes).
    Removing ui_main.o(.text), (0 bytes).
    Removing ui_main.o(.text.UI_Main_Initial), (148 bytes).
    Removing ui_main.o(.ARM.exidx.text.UI_Main_Initial), (8 bytes).
    Removing ui_main.o(.text.UI_Main_Operation), (60 bytes).
    Removing ui_main.o(.ARM.exidx.text.UI_Main_Operation), (8 bytes).
    Removing ui_menu.o(.text), (0 bytes).
    Removing ui_menu.o(.text.UI_Menu_Initial), (120 bytes).
    Removing ui_menu.o(.ARM.exidx.text.UI_Menu_Initial), (8 bytes).
    Removing ui_menu.o(.text.UI_Menu_Operation), (284 bytes).
    Removing ui_menu.o(.ARM.exidx.text.UI_Menu_Operation), (8 bytes).
    Removing ui_menu.o(.bss.Menu_x), (1 bytes).
    Removing ui_menu.o(.rodata.str1.1), (39 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SERVO1_PWM_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SERVO2_PWM_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_QEI_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_CAPTURE_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYS_TIMER_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_HC05_UART_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SENSER_UART_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SERVO_UART_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_LCD_SPI_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_Cross_Trigger_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (100 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (112 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing delay.o(.text.delay_us), (10 bytes).
    Removing delay.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_Test), (2 bytes).
    Removing led.o(.ARM.exidx.text.LED_Test), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.ARM.exidx.text.Check_Key), (8 bytes).
    Removing uart.o(.text), (0 bytes).
    Removing uart.o(.text.UART_Test), (136 bytes).
    Removing uart.o(.ARM.exidx.text.UART_Test), (8 bytes).
    Removing uart.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing uart.o(.bss.txData), (1 bytes).
    Removing timer.o(.text), (0 bytes).
    Removing timer.o(.ARM.exidx.text.Timer_Start), (8 bytes).
    Removing timer.o(.text.Timer_PWM_Test), (96 bytes).
    Removing timer.o(.ARM.exidx.text.Timer_PWM_Test), (8 bytes).
    Removing timer.o(.bss.PWMx), (2 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteCmd), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Fill), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (100 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteDat), (8 bytes).
    Removing oled_user.o(.text), (0 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_Refresh_AllGDRAM), (8 bytes).
    Removing oled_user.o(.text.OLEDLCD_ClearBuffer), (20 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_ClearBuffer), (8 bytes).
    Removing oled_user.o(.text.OLEDLCD_DrawDot), (72 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_DrawDot), (8 bytes).
    Removing oled_user.o(.text.OLEDLCD_Put6x12Char), (164 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x12Char), (8 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x7Char), (8 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_Put12x12CNstr), (8 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x7ENstr), (8 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x12Num), (8 bytes).
    Removing oled_user.o(.ARM.exidx.text.OLEDLCD_Put6x7Num), (8 bytes).
    Removing oled_user.o(.rodata.BMP_shilubi), (704 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.ARM.exidx.text.Set_Motor), (8 bytes).
    Removing buzzer.o(.text), (0 bytes).
    Removing buzzer.o(.text.BEEP_test), (2 bytes).
    Removing buzzer.o(.ARM.exidx.text.BEEP_test), (8 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (80 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (192 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (196 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (84 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

319 unused section(s) (total 8495 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  aeabi_memset4.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    KEY.c                                    0x00000000   Number         0  key.o ABSOLUTE
    LED.c                                    0x00000000   Number         0  led.o ABSOLUTE
    MOTOR.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    SYSTEM/startup_mspm0g350x_uvision.s      0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    Timer.c                                  0x00000000   Number         0  timer.o ABSOLUTE
    UART.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    UI_Main.c                                0x00000000   Number         0  ui_main.o ABSOLUTE
    UI_Menu.c                                0x00000000   Number         0  ui_menu.o ABSOLUTE
    User_Control.c                           0x00000000   Number         0  user_control.o ABSOLUTE
    User_IR_Sensor.c                         0x00000000   Number         0  user_ir_sensor.o ABSOLUTE
    User_Parameter.c                         0x00000000   Number         0  user_parameter.o ABSOLUTE
    buzzer.c                                 0x00000000   Number         0  buzzer.o ABSOLUTE
    counting.c                               0x00000000   Number         0  counting.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    delay.c                                  0x00000000   Number         0  delay.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    oled_user.c                              0x00000000   Number         0  oled_user.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x00000164   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000166   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000168   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000016a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000016a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000016c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000016c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000016c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000172   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000176   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000176   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000017e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000180   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000180   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000184   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0000018c   Section       44  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001b8   Section        0  aeabi_memset.o(.text)
    .text                                    0x000001cc   Section      504  aeabi_sdivfast.o(.text)
    .text                                    0x000003c4   Section        0  heapauxi.o(.text)
    .text                                    0x000003ca   Section        0  rt_memclr.o(.text)
    .text                                    0x0000040a   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x00000448   Section        0  exit.o(.text)
    .text                                    0x00000458   Section        8  libspace.o(.text)
    .text                                    0x00000460   Section        0  sys_exit.o(.text)
    .text                                    0x0000046c   Section        2  use_no_semi.o(.text)
    .text                                    0x0000046e   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x00000470   Section        0  user_ir_sensor.o(.text.ADC0_IRQHandler)
    __arm_cp.0_0                             0x000004b4   Number         4  user_ir_sensor.o(.text.ADC0_IRQHandler)
    __arm_cp.0_1                             0x000004b8   Number         4  user_ir_sensor.o(.text.ADC0_IRQHandler)
    __arm_cp.0_3                             0x000004bc   Number         4  user_ir_sensor.o(.text.ADC0_IRQHandler)
    __arm_cp.0_4                             0x000004c0   Number         4  user_ir_sensor.o(.text.ADC0_IRQHandler)
    [Anonymous Symbol]                       0x000004c4   Section        0  user_ir_sensor.o(.text.ADC1_IRQHandler)
    __arm_cp.1_0                             0x000004f0   Number         4  user_ir_sensor.o(.text.ADC1_IRQHandler)
    __arm_cp.1_1                             0x000004f4   Number         4  user_ir_sensor.o(.text.ADC1_IRQHandler)
    __arm_cp.1_3                             0x000004f8   Number         4  user_ir_sensor.o(.text.ADC1_IRQHandler)
    __arm_cp.1_4                             0x000004fc   Number         4  user_ir_sensor.o(.text.ADC1_IRQHandler)
    [Anonymous Symbol]                       0x00000500   Section        0  user_ir_sensor.o(.text.ADC_Init)
    __arm_cp.2_0                             0x0000050c   Number         4  user_ir_sensor.o(.text.ADC_Init)
    [Anonymous Symbol]                       0x00000510   Section        0  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_16                            0x000007a4   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_17                            0x000007a8   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_0                             0x00000924   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_3                             0x00000928   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_4                             0x0000092c   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_5                             0x00000930   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_6                             0x00000934   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_7                             0x00000938   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_8                             0x0000093c   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_9                             0x00000940   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_10                            0x00000944   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    __arm_cp.7_11                            0x00000948   Number         4  user_ir_sensor.o(.text.ADC_Transform)
    [Anonymous Symbol]                       0x0000094c   Section        0  user_ir_sensor.o(.text.ADC_start)
    __arm_cp.3_0                             0x00000964   Number         4  user_ir_sensor.o(.text.ADC_start)
    __arm_cp.3_1                             0x00000968   Number         4  user_ir_sensor.o(.text.ADC_start)
    [Anonymous Symbol]                       0x0000096c   Section        0  user_ir_sensor.o(.text.Binarization_Display)
    __arm_cp.5_1                             0x00000a28   Number         4  user_ir_sensor.o(.text.Binarization_Display)
    __arm_cp.5_2                             0x00000a2c   Number         4  user_ir_sensor.o(.text.Binarization_Display)
    __arm_cp.5_4                             0x00000a30   Number         4  user_ir_sensor.o(.text.Binarization_Display)
    __arm_cp.5_5                             0x00000a34   Number         4  user_ir_sensor.o(.text.Binarization_Display)
    [Anonymous Symbol]                       0x00000a38   Section        0  key.o(.text.Check_Key)
    __arm_cp.0_0                             0x00000be8   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_1                             0x00000bec   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_2                             0x00000bf0   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_3                             0x00000bf4   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_4                             0x00000bf8   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_5                             0x00000bfc   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_6                             0x00000c00   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_7                             0x00000c04   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_8                             0x00000c08   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_9                             0x00000c0c   Number         4  key.o(.text.Check_Key)
    __arm_cp.0_10                            0x00000c10   Number         4  key.o(.text.Check_Key)
    [Anonymous Symbol]                       0x00000c14   Section        0  user_control.o(.text.Control)
    __arm_cp.0_0                             0x00000cb0   Number         4  user_control.o(.text.Control)
    __arm_cp.0_1                             0x00000cb4   Number         4  user_control.o(.text.Control)
    __arm_cp.0_2                             0x00000cb8   Number         4  user_control.o(.text.Control)
    __arm_cp.0_3                             0x00000cbc   Number         4  user_control.o(.text.Control)
    __arm_cp.0_4                             0x00000cc0   Number         4  user_control.o(.text.Control)
    __arm_cp.0_5                             0x00000cc4   Number         4  user_control.o(.text.Control)
    __arm_cp.0_6                             0x00000cc8   Number         4  user_control.o(.text.Control)
    __arm_cp.0_7                             0x00000ccc   Number         4  user_control.o(.text.Control)
    __arm_cp.0_8                             0x00000cd0   Number         4  user_control.o(.text.Control)
    __arm_cp.0_9                             0x00000cd4   Number         4  user_control.o(.text.Control)
    __arm_cp.0_10                            0x00000cd8   Number         4  user_control.o(.text.Control)
    __arm_cp.0_11                            0x00000cdc   Number         4  user_control.o(.text.Control)
    __arm_cp.0_12                            0x00000ce0   Number         4  user_control.o(.text.Control)
    [Anonymous Symbol]                       0x00000ce4   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x00000d1c   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x00000d20   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    [Anonymous Symbol]                       0x00000d24   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00000d2e   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    [Anonymous Symbol]                       0x00000d54   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x00000d90   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x00000d94   Number         4  dl_spi.o(.text.DL_SPI_init)
    [Anonymous Symbol]                       0x00000d98   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    [Anonymous Symbol]                       0x00000dac   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_0                             0x00000e60   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00000e64   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00000e68   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00000e6c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    [Anonymous Symbol]                       0x00000e70   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00000e90   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00000e94   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    [Anonymous Symbol]                       0x00000e98   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    __arm_cp.39_0                            0x00000f08   Number         4  dl_timer.o(.text.DL_TimerA_initPWMMode)
    [Anonymous Symbol]                       0x00000f0c   Section        0  dl_timer.o(.text.DL_Timer_getCaptureCompareCtl)
    [Anonymous Symbol]                       0x00000f1c   Section        0  dl_timer.o(.text.DL_Timer_initCaptureMode)
    __arm_cp.5_1                             0x00001010   Number         4  dl_timer.o(.text.DL_Timer_initCaptureMode)
    __arm_cp.5_4                             0x00001014   Number         4  dl_timer.o(.text.DL_Timer_initCaptureMode)
    __arm_cp.5_5                             0x00001018   Number         4  dl_timer.o(.text.DL_Timer_initCaptureMode)
    [Anonymous Symbol]                       0x0000101c   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_1                            0x000010c8   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_2                            0x000010cc   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_3                            0x000010d0   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_4                            0x000010d4   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_6                            0x000010d8   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x000010dc   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x000011bc   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x000011c0   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x000011c4   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x000011dc   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    __arm_cp.4_0                             0x000011f8   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    __arm_cp.4_1                             0x000011fc   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    __arm_cp.4_2                             0x00001200   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    [Anonymous Symbol]                       0x00001204   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareInput)
    [Anonymous Symbol]                       0x00001220   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.13_0                            0x00001234   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00001238   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00001244   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00001248   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00001260   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00001264   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x000012a4   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x000012a8   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x000012ac   Section        0  dl_uart.o(.text.DL_UART_receiveDataBlocking)
    __arm_cp.6_0                             0x000012bc   Number         4  dl_uart.o(.text.DL_UART_receiveDataBlocking)
    [Anonymous Symbol]                       0x000012c0   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x000012d4   Section        0  user_ir_sensor.o(.text.Display_ADC)
    __arm_cp.4_0                             0x00001320   Number         4  user_ir_sensor.o(.text.Display_ADC)
    [Anonymous Symbol]                       0x00001324   Section        0  counting.o(.text.Display_Counting_Info)
    __arm_cp.2_1                             0x00001394   Number         4  counting.o(.text.Display_Counting_Info)
    __arm_cp.2_3                             0x0000139c   Number         4  counting.o(.text.Display_Counting_Info)
    __arm_cp.2_5                             0x000013a4   Number         4  counting.o(.text.Display_Counting_Info)
    [Anonymous Symbol]                       0x000013a8   Section        0  oled_user.o(.text.OLEDLCD_Put12x12CNstr)
    __arm_cp.5_0                             0x000015b0   Number         4  oled_user.o(.text.OLEDLCD_Put12x12CNstr)
    __arm_cp.5_2                             0x000015b4   Number         4  oled_user.o(.text.OLEDLCD_Put12x12CNstr)
    [Anonymous Symbol]                       0x000015b8   Section        0  oled_user.o(.text.OLEDLCD_Put6x12Num)
    [Anonymous Symbol]                       0x0000167c   Section        0  oled_user.o(.text.OLEDLCD_Put6x7Char)
    __arm_cp.4_0                             0x00001770   Number         4  oled_user.o(.text.OLEDLCD_Put6x7Char)
    __arm_cp.4_1                             0x00001774   Number         4  oled_user.o(.text.OLEDLCD_Put6x7Char)
    [Anonymous Symbol]                       0x00001778   Section        0  oled_user.o(.text.OLEDLCD_Put6x7ENstr)
    [Anonymous Symbol]                       0x000017a6   Section        0  oled_user.o(.text.OLEDLCD_Put6x7Num)
    [Anonymous Symbol]                       0x00001884   Section        0  oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM)
    __arm_cp.0_0                             0x000018c0   Number         4  oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM)
    [Anonymous Symbol]                       0x000018c4   Section        0  oled.o(.text.OLED_Fill)
    [Anonymous Symbol]                       0x00001938   Section        0  oled.o(.text.OLED_Init)
    __arm_cp.0_0                             0x00001c4c   Number         4  oled.o(.text.OLED_Init)
    __arm_cp.0_2                             0x00001c50   Number         4  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x00001c54   Section        0  oled.o(.text.OLED_WriteCmd)
    __arm_cp.1_0                             0x00001c78   Number         4  oled.o(.text.OLED_WriteCmd)
    [Anonymous Symbol]                       0x00001c7c   Section        0  oled.o(.text.OLED_WriteDat)
    __arm_cp.4_0                             0x00001c88   Number         4  oled.o(.text.OLED_WriteDat)
    [Anonymous Symbol]                       0x00001c8c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.15_0                            0x00001d04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.15_1                            0x00001d08   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.15_4                            0x00001d0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.15_5                            0x00001d10   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.15_6                            0x00001d14   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    [Anonymous Symbol]                       0x00001d18   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_0                            0x00001d78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_1                            0x00001d7c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_2                            0x00001d80   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_3                            0x00001d84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_4                            0x00001d88   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_5                            0x00001d8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_6                            0x00001d90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_7                            0x00001d94   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    __arm_cp.16_8                            0x00001d98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    [Anonymous Symbol]                       0x00001d9c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init)
    __arm_cp.8_0                             0x00001db8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init)
    __arm_cp.8_1                             0x00001dbc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init)
    __arm_cp.8_2                             0x00001dc0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init)
    __arm_cp.8_3                             0x00001dc4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init)
    [Anonymous Symbol]                       0x00001dc8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00001ed4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00001edc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00001ee0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00001ee4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00001ee8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00001eec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00001ef0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001ef4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init)
    __arm_cp.11_0                            0x00001f40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init)
    __arm_cp.11_1                            0x00001f44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init)
    __arm_cp.11_2                            0x00001f48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init)
    __arm_cp.11_3                            0x00001f4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init)
    [Anonymous Symbol]                       0x00001f50   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.10_0                            0x00001f98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.10_1                            0x00001f9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.10_2                            0x00001fa0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00001fa4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init)
    __arm_cp.14_0                            0x00001fec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init)
    __arm_cp.14_1                            0x00001ff0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init)
    __arm_cp.14_2                            0x00001ff4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init)
    __arm_cp.14_3                            0x00001ff8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init)
    __arm_cp.14_4                            0x00001ffc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init)
    [Anonymous Symbol]                       0x00002000   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_0                             0x000020d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_1                             0x000020d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_2                             0x000020d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_3                             0x000020dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_4                             0x000020e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    [Anonymous Symbol]                       0x000020e4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Cross_Trigger_init)
    [Anonymous Symbol]                       0x000020e8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_0                             0x00002114   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_1                             0x00002118   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_2                             0x0000211c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_3                             0x00002120   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_4                             0x00002124   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_5                             0x00002128   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_6                             0x0000212c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.7_7                             0x00002130   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    [Anonymous Symbol]                       0x00002134   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init)
    __arm_cp.12_0                            0x00002184   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init)
    __arm_cp.12_1                            0x00002188   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init)
    __arm_cp.12_2                            0x0000218c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init)
    __arm_cp.12_3                            0x00002190   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init)
    __arm_cp.12_4                            0x00002194   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init)
    [Anonymous Symbol]                       0x00002198   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    __arm_cp.5_0                             0x00002260   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    __arm_cp.5_1                             0x00002264   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    __arm_cp.5_2                             0x00002268   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    __arm_cp.5_3                             0x0000226c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    __arm_cp.5_4                             0x00002270   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    __arm_cp.5_5                             0x00002274   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    [Anonymous Symbol]                       0x00002278   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init)
    __arm_cp.6_0                             0x000022e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init)
    __arm_cp.6_1                             0x000022ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init)
    __arm_cp.6_2                             0x000022f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init)
    __arm_cp.6_3                             0x000022f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init)
    [Anonymous Symbol]                       0x000022f8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init)
    __arm_cp.13_0                            0x00002348   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init)
    __arm_cp.13_1                            0x0000234c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init)
    __arm_cp.13_2                            0x00002350   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init)
    __arm_cp.13_3                            0x00002354   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init)
    __arm_cp.13_4                            0x00002358   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init)
    [Anonymous Symbol]                       0x0000235c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x000023ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x000023b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_2                             0x000023b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x000023b8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init)
    __arm_cp.9_0                             0x000023dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init)
    __arm_cp.9_1                             0x000023e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init)
    __arm_cp.9_2                             0x000023e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init)
    __arm_cp.9_3                             0x000023e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init)
    __arm_cp.9_4                             0x000023ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init)
    [Anonymous Symbol]                       0x000023f0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00002454   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00002458   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x0000245c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_3                             0x00002460   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_4                             0x00002464   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00002468   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x000024e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x000024ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x000024f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x000024f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x000024f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x000024fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00002500   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00002504   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00002508   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_9                             0x0000250c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_10                            0x00002510   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_11                            0x00002514   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_12                            0x00002518   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_13                            0x0000251c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_14                            0x00002520   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_15                            0x00002524   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_16                            0x00002528   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x0000252c   Section        0  motor.o(.text.Set_Motor)
    __arm_cp.0_0                             0x000025b0   Number         4  motor.o(.text.Set_Motor)
    __arm_cp.0_1                             0x000025b4   Number         4  motor.o(.text.Set_Motor)
    __arm_cp.0_4                             0x000025b8   Number         4  motor.o(.text.Set_Motor)
    [Anonymous Symbol]                       0x000025bc   Section        0  main.o(.text.TIMG12_IRQHandler)
    __arm_cp.1_0                             0x00002624   Number         4  main.o(.text.TIMG12_IRQHandler)
    __arm_cp.1_1                             0x00002628   Number         4  main.o(.text.TIMG12_IRQHandler)
    __arm_cp.1_2                             0x0000262c   Number         4  main.o(.text.TIMG12_IRQHandler)
    __arm_cp.1_3                             0x00002630   Number         4  main.o(.text.TIMG12_IRQHandler)
    __arm_cp.1_4                             0x00002634   Number         4  main.o(.text.TIMG12_IRQHandler)
    __arm_cp.1_5                             0x00002638   Number         4  main.o(.text.TIMG12_IRQHandler)
    [Anonymous Symbol]                       0x0000263c   Section        0  timer.o(.text.Timer_Start)
    __arm_cp.0_0                             0x00002668   Number         4  timer.o(.text.Timer_Start)
    __arm_cp.0_1                             0x0000266c   Number         4  timer.o(.text.Timer_Start)
    __arm_cp.0_2                             0x00002670   Number         4  timer.o(.text.Timer_Start)
    __arm_cp.0_3                             0x00002674   Number         4  timer.o(.text.Timer_Start)
    __arm_cp.0_4                             0x00002678   Number         4  timer.o(.text.Timer_Start)
    [Anonymous Symbol]                       0x0000267c   Section        0  uart.o(.text.UART0_IRQHandler)
    __arm_cp.1_0                             0x00002698   Number         4  uart.o(.text.UART0_IRQHandler)
    __arm_cp.1_1                             0x0000269c   Number         4  uart.o(.text.UART0_IRQHandler)
    __arm_cp.1_2                             0x000026a0   Number         4  uart.o(.text.UART0_IRQHandler)
    __arm_cp.1_3                             0x000026a4   Number         4  uart.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x000026a8   Section        0  counting.o(.text.counting)
    __arm_cp.0_1                             0x00002714   Number         4  counting.o(.text.counting)
    __arm_cp.0_2                             0x00002718   Number         4  counting.o(.text.counting)
    __arm_cp.0_3                             0x0000271c   Number         4  counting.o(.text.counting)
    __arm_cp.0_4                             0x00002720   Number         4  counting.o(.text.counting)
    __arm_cp.0_5                             0x00002724   Number         4  counting.o(.text.counting)
    __arm_cp.0_6                             0x00002728   Number         4  counting.o(.text.counting)
    [Anonymous Symbol]                       0x0000272c   Section        0  delay.o(.text.delay_ms)
    [Anonymous Symbol]                       0x0000273c   Section        0  counting.o(.text.keycontrol)
    __arm_cp.1_0                             0x00002784   Number         4  counting.o(.text.keycontrol)
    __arm_cp.1_1                             0x00002788   Number         4  counting.o(.text.keycontrol)
    __arm_cp.1_2                             0x0000278c   Number         4  counting.o(.text.keycontrol)
    __arm_cp.1_3                             0x00002790   Number         4  counting.o(.text.keycontrol)
    [Anonymous Symbol]                       0x00002794   Section        0  main.o(.text.main)
    __arm_cp.0_0                             0x00002814   Number         4  main.o(.text.main)
    __arm_cp.0_1                             0x00002818   Number         4  main.o(.text.main)
    __arm_cp.0_2                             0x0000281c   Number         4  main.o(.text.main)
    __arm_cp.0_3                             0x00002820   Number         4  main.o(.text.main)
    .text_divfast                            0x00002824   Section      502  aeabi_sdivfast.o(.text_divfast)
    [Anonymous Symbol]                       0x00002a1c   Section        0  dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode)
    ASCII6X12                                0x00002c08   Data        1152  oled_user.o(.rodata.ASCII6X12)
    [Anonymous Symbol]                       0x00002c08   Section        0  oled_user.o(.rodata.ASCII6X12)
    gADC12_0ClockConfig                      0x00003088   Data           8  ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig)
    [Anonymous Symbol]                       0x00003088   Section        0  ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig)
    gADC12_1ClockConfig                      0x00003090   Data           8  ti_msp_dl_config.o(.rodata.gADC12_1ClockConfig)
    [Anonymous Symbol]                       0x00003090   Section        0  ti_msp_dl_config.o(.rodata.gADC12_1ClockConfig)
    gCAPTURE_0CaptureConfig                  0x00003098   Data          16  ti_msp_dl_config.o(.rodata.gCAPTURE_0CaptureConfig)
    [Anonymous Symbol]                       0x00003098   Section        0  ti_msp_dl_config.o(.rodata.gCAPTURE_0CaptureConfig)
    gCAPTURE_0ClockConfig                    0x000030a8   Data           3  ti_msp_dl_config.o(.rodata.gCAPTURE_0ClockConfig)
    [Anonymous Symbol]                       0x000030a8   Section        0  ti_msp_dl_config.o(.rodata.gCAPTURE_0ClockConfig)
    gHC05_UARTClockConfig                    0x000030ab   Data           2  ti_msp_dl_config.o(.rodata.gHC05_UARTClockConfig)
    [Anonymous Symbol]                       0x000030ab   Section        0  ti_msp_dl_config.o(.rodata.gHC05_UARTClockConfig)
    gHC05_UARTConfig                         0x000030ae   Data          10  ti_msp_dl_config.o(.rodata.gHC05_UARTConfig)
    [Anonymous Symbol]                       0x000030ae   Section        0  ti_msp_dl_config.o(.rodata.gHC05_UARTConfig)
    gI2C_0ClockConfig                        0x000030b8   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x000030b8   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gLCD_SPI_clockConfig                     0x000030ba   Data           2  ti_msp_dl_config.o(.rodata.gLCD_SPI_clockConfig)
    [Anonymous Symbol]                       0x000030ba   Section        0  ti_msp_dl_config.o(.rodata.gLCD_SPI_clockConfig)
    gLCD_SPI_config                          0x000030bc   Data          10  ti_msp_dl_config.o(.rodata.gLCD_SPI_config)
    [Anonymous Symbol]                       0x000030bc   Section        0  ti_msp_dl_config.o(.rodata.gLCD_SPI_config)
    gMOTOR_PWMClockConfig                    0x000030c6   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    [Anonymous Symbol]                       0x000030c6   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    gMOTOR_PWMConfig                         0x000030cc   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    [Anonymous Symbol]                       0x000030cc   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    gQEI_0ClockConfig                        0x000030d4   Data           3  ti_msp_dl_config.o(.rodata.gQEI_0ClockConfig)
    [Anonymous Symbol]                       0x000030d4   Section        0  ti_msp_dl_config.o(.rodata.gQEI_0ClockConfig)
    gSENSER_UARTClockConfig                  0x000030d7   Data           2  ti_msp_dl_config.o(.rodata.gSENSER_UARTClockConfig)
    [Anonymous Symbol]                       0x000030d7   Section        0  ti_msp_dl_config.o(.rodata.gSENSER_UARTClockConfig)
    gSENSER_UARTConfig                       0x000030da   Data          10  ti_msp_dl_config.o(.rodata.gSENSER_UARTConfig)
    [Anonymous Symbol]                       0x000030da   Section        0  ti_msp_dl_config.o(.rodata.gSENSER_UARTConfig)
    gSERVO1_PWMClockConfig                   0x000030e4   Data           3  ti_msp_dl_config.o(.rodata.gSERVO1_PWMClockConfig)
    [Anonymous Symbol]                       0x000030e4   Section        0  ti_msp_dl_config.o(.rodata.gSERVO1_PWMClockConfig)
    gSERVO1_PWMConfig                        0x000030e8   Data           8  ti_msp_dl_config.o(.rodata.gSERVO1_PWMConfig)
    [Anonymous Symbol]                       0x000030e8   Section        0  ti_msp_dl_config.o(.rodata.gSERVO1_PWMConfig)
    gSERVO2_PWMClockConfig                   0x000030f0   Data           3  ti_msp_dl_config.o(.rodata.gSERVO2_PWMClockConfig)
    [Anonymous Symbol]                       0x000030f0   Section        0  ti_msp_dl_config.o(.rodata.gSERVO2_PWMClockConfig)
    gSERVO2_PWMConfig                        0x000030f4   Data           8  ti_msp_dl_config.o(.rodata.gSERVO2_PWMConfig)
    [Anonymous Symbol]                       0x000030f4   Section        0  ti_msp_dl_config.o(.rodata.gSERVO2_PWMConfig)
    gSERVO_UARTClockConfig                   0x000030fc   Data           2  ti_msp_dl_config.o(.rodata.gSERVO_UARTClockConfig)
    [Anonymous Symbol]                       0x000030fc   Section        0  ti_msp_dl_config.o(.rodata.gSERVO_UARTClockConfig)
    gSERVO_UARTConfig                        0x000030fe   Data          10  ti_msp_dl_config.o(.rodata.gSERVO_UARTConfig)
    [Anonymous Symbol]                       0x000030fe   Section        0  ti_msp_dl_config.o(.rodata.gSERVO_UARTConfig)
    gSYSPLLConfig                            0x00003108   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00003108   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gSYS_TIMERClockConfig                    0x00003130   Data           3  ti_msp_dl_config.o(.rodata.gSYS_TIMERClockConfig)
    [Anonymous Symbol]                       0x00003130   Section        0  ti_msp_dl_config.o(.rodata.gSYS_TIMERClockConfig)
    gSYS_TIMERTimerConfig                    0x00003134   Data          20  ti_msp_dl_config.o(.rodata.gSYS_TIMERTimerConfig)
    [Anonymous Symbol]                       0x00003134   Section        0  ti_msp_dl_config.o(.rodata.gSYS_TIMERTimerConfig)
    hanzi_12x12                              0x00003148   Data        1326  oled_user.o(.rodata.hanzi_12x12)
    [Anonymous Symbol]                       0x00003148   Section        0  oled_user.o(.rodata.hanzi_12x12)
    [Anonymous Symbol]                       0x00003676   Section        0  user_ir_sensor.o(.rodata.str1.1)
    .bss                                     0x20200020   Section       96  libspace.o(.bss)
    delay_counter                            0x202004e0   Data           4  counting.o(.bss.delay_counter)
    [Anonymous Symbol]                       0x202004e0   Section        0  counting.o(.bss.delay_counter)
    key_delay_counter                        0x2020077c   Data           4  counting.o(.bss.key_delay_counter)
    [Anonymous Symbol]                       0x2020077c   Section        0  counting.o(.bss.key_delay_counter)
    key_pressed                              0x20200780   Data           1  counting.o(.bss.key_pressed)
    [Anonymous Symbol]                       0x20200780   Section        0  counting.o(.bss.key_pressed)
    lap_timer                                0x20200784   Data           4  counting.o(.bss.lap_timer)
    [Anonymous Symbol]                       0x20200784   Section        0  counting.o(.bss.lap_timer)
    total_time_limit                         0x2020078c   Data           4  counting.o(.bss.total_time_limit)
    [Anonymous Symbol]                       0x2020078c   Section        0  counting.o(.bss.total_time_limit)
    Heap_Mem                                 0x20200790   Data           0  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200790   Data         256  startup_mspm0g350x_uvision.o(STACK)
    HEAP                                     0x20200790   Section        0  startup_mspm0g350x_uvision.o(HEAP)
    STACK                                    0x20200790   Section      256  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20200890   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x00000165   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000169   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000016b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000016d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000016d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000016d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000177   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000177   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000017f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000185   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0000018d   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x00000191   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x00000193   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x00000195   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x00000197   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x00000199   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x0000019b   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x0000019b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x0000019d   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memset                           0x000001b9   Thumb Code    18  aeabi_memset.o(.text)
    __aeabi_uidivmod                         0x000001cd   Thumb Code    28  aeabi_sdivfast.o(.text)
    __aeabi_idivmod                          0x000001e9   Thumb Code   472  aeabi_sdivfast.o(.text)
    __use_two_region_memory                  0x000003c5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x000003c7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000003c9   Thumb Code     2  heapauxi.o(.text)
    _memset_w                                0x000003cb   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x000003e5   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x00000403   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x00000403   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x00000407   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x00000407   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x00000407   Thumb Code     4  rt_memclr.o(.text)
    __user_setup_stackheap                   0x0000040b   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x00000449   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x00000459   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x00000459   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x00000459   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x00000461   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0000046d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0000046d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0000046f   Thumb Code     0  indicate_semi.o(.text)
    ADC0_IRQHandler                          0x00000471   Thumb Code    68  user_ir_sensor.o(.text.ADC0_IRQHandler)
    ADC1_IRQHandler                          0x000004c5   Thumb Code    44  user_ir_sensor.o(.text.ADC1_IRQHandler)
    ADC_Init                                 0x00000501   Thumb Code    12  user_ir_sensor.o(.text.ADC_Init)
    ADC_Transform                            0x00000511   Thumb Code  1044  user_ir_sensor.o(.text.ADC_Transform)
    ADC_start                                0x0000094d   Thumb Code    24  user_ir_sensor.o(.text.ADC_start)
    Binarization_Display                     0x0000096d   Thumb Code   188  user_ir_sensor.o(.text.Binarization_Display)
    Check_Key                                0x00000a39   Thumb Code   432  key.o(.text.Check_Key)
    Control                                  0x00000c15   Thumb Code   156  user_control.o(.text.Control)
    DL_ADC12_setClockConfig                  0x00000ce5   Thumb Code    64  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x00000d25   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_I2C_setClockConfig                    0x00000d2f   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SPI_init                              0x00000d55   Thumb Code    68  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x00000d99   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x00000dad   Thumb Code   196  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00000e71   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_TimerA_initPWMMode                    0x00000e99   Thumb Code   116  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_getCaptureCompareCtl            0x00000f0d   Thumb Code    16  dl_timer.o(.text.DL_Timer_getCaptureCompareCtl)
    DL_Timer_initCaptureMode                 0x00000f1d   Thumb Code   256  dl_timer.o(.text.DL_Timer_initCaptureMode)
    DL_Timer_initPWMMode                     0x0000101d   Thumb Code   192  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x000010dd   Thumb Code   232  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x000011c5   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareCtl            0x000011dd   Thumb Code    40  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    DL_Timer_setCaptureCompareInput          0x00001205   Thumb Code    26  dl_timer.o(.text.DL_Timer_setCaptureCompareInput)
    DL_Timer_setCaptureCompareOutCtl         0x00001221   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00001239   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00001249   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00001265   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_receiveDataBlocking              0x000012ad   Thumb Code    20  dl_uart.o(.text.DL_UART_receiveDataBlocking)
    DL_UART_setClockConfig                   0x000012c1   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    Display_ADC                              0x000012d5   Thumb Code    76  user_ir_sensor.o(.text.Display_ADC)
    Display_Counting_Info                    0x00001325   Thumb Code   108  counting.o(.text.Display_Counting_Info)
    OLEDLCD_Put12x12CNstr                    0x000013a9   Thumb Code   520  oled_user.o(.text.OLEDLCD_Put12x12CNstr)
    OLEDLCD_Put6x12Num                       0x000015b9   Thumb Code   196  oled_user.o(.text.OLEDLCD_Put6x12Num)
    OLEDLCD_Put6x7Char                       0x0000167d   Thumb Code   244  oled_user.o(.text.OLEDLCD_Put6x7Char)
    OLEDLCD_Put6x7ENstr                      0x00001779   Thumb Code    46  oled_user.o(.text.OLEDLCD_Put6x7ENstr)
    OLEDLCD_Put6x7Num                        0x000017a7   Thumb Code   222  oled_user.o(.text.OLEDLCD_Put6x7Num)
    OLEDLCD_Refresh_AllGDRAM                 0x00001885   Thumb Code    60  oled_user.o(.text.OLEDLCD_Refresh_AllGDRAM)
    OLED_Fill                                0x000018c5   Thumb Code   116  oled.o(.text.OLED_Fill)
    OLED_Init                                0x00001939   Thumb Code   788  oled.o(.text.OLED_Init)
    OLED_WriteCmd                            0x00001c55   Thumb Code    36  oled.o(.text.OLED_WriteCmd)
    OLED_WriteDat                            0x00001c7d   Thumb Code    12  oled.o(.text.OLED_WriteDat)
    SYSCFG_DL_ADC12_0_init                   0x00001c8d   Thumb Code   120  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    SYSCFG_DL_ADC12_1_init                   0x00001d19   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_1_init)
    SYSCFG_DL_CAPTURE_0_init                 0x00001d9d   Thumb Code    28  ti_msp_dl_config.o(.text.SYSCFG_DL_CAPTURE_0_init)
    SYSCFG_DL_GPIO_init                      0x00001dc9   Thumb Code   268  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_HC05_UART_init                 0x00001ef5   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_HC05_UART_init)
    SYSCFG_DL_I2C_0_init                     0x00001f51   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_LCD_SPI_init                   0x00001fa5   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_LCD_SPI_init)
    SYSCFG_DL_MOTOR_PWM_init                 0x00002001   Thumb Code   208  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    SYSCFG_DL_PWM_Cross_Trigger_init         0x000020e5   Thumb Code     2  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_Cross_Trigger_init)
    SYSCFG_DL_QEI_0_init                     0x000020e9   Thumb Code    44  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    SYSCFG_DL_SENSER_UART_init               0x00002135   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_SENSER_UART_init)
    SYSCFG_DL_SERVO1_PWM_init                0x00002199   Thumb Code   200  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO1_PWM_init)
    SYSCFG_DL_SERVO2_PWM_init                0x00002279   Thumb Code   112  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO2_PWM_init)
    SYSCFG_DL_SERVO_UART_init                0x000022f9   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_SERVO_UART_init)
    SYSCFG_DL_SYSCTL_init                    0x0000235d   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYS_TIMER_init                 0x000023b9   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_SYS_TIMER_init)
    SYSCFG_DL_init                           0x000023f1   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00002469   Thumb Code   128  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Set_Motor                                0x0000252d   Thumb Code   132  motor.o(.text.Set_Motor)
    TIMG12_IRQHandler                        0x000025bd   Thumb Code   104  main.o(.text.TIMG12_IRQHandler)
    Timer_Start                              0x0000263d   Thumb Code    44  timer.o(.text.Timer_Start)
    UART0_IRQHandler                         0x0000267d   Thumb Code    28  uart.o(.text.UART0_IRQHandler)
    counting                                 0x000026a9   Thumb Code   108  counting.o(.text.counting)
    delay_ms                                 0x0000272d   Thumb Code    14  delay.o(.text.delay_ms)
    keycontrol                               0x0000273d   Thumb Code    72  counting.o(.text.keycontrol)
    main                                     0x00002795   Thumb Code   128  main.o(.text.main)
    __aeabi_uidiv                            0x00002825   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x00002869   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    ASCII5x7                                 0x00002a28   Data         480  oled_user.o(.rodata.ASCII5x7)
    Region$$Table$$Base                      0x00003680   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000036a0   Number         0  anon$$obj.o(Region$$Table)
    ADC_Threshold                            0x20200000   Data          12  user_ir_sensor.o(.data.ADC_Threshold)
    Line_Out_timesStik                       0x2020000c   Data           4  user_control.o(.data.Line_Out_timesStik)
    Mode                                     0x20200010   Data           1  main.o(.data.Mode)
    PID_P                                    0x20200014   Data           4  user_parameter.o(.data.PID_P)
    Speed                                    0x20200018   Data           4  user_parameter.o(.data.Speed)
    Target_Laps                              0x2020001c   Data           4  counting.o(.data.Target_Laps)
    __libspace_start                         0x20200020   Data          96  libspace.o(.bss)
    ADC1_Flag                                0x20200080   Data           1  user_ir_sensor.o(.bss.ADC1_Flag)
    __temporary_stack_top$libspace           0x20200080   Data           0  libspace.o(.bss)
    ADC_Data                                 0x20200081   Data          12  user_ir_sensor.o(.bss.ADC_Data)
    ADC_Flag                                 0x2020008d   Data           1  user_ir_sensor.o(.bss.ADC_Flag)
    Error                                    0x20200090   Data           4  user_ir_sensor.o(.bss.Error)
    IR_RES                                   0x20200094   Data           2  user_ir_sensor.o(.bss.IR_RES)
    IR_RES_For_Error                         0x20200096   Data           2  user_ir_sensor.o(.bss.IR_RES_For_Error)
    IR_RES_Original                          0x20200098   Data           2  user_ir_sensor.o(.bss.IR_RES_Original)
    KEYD_Flag                                0x2020009a   Data           1  key.o(.bss.KEYD_Flag)
    KEYD_Times                               0x2020009c   Data           4  key.o(.bss.KEYD_Times)
    KEYL_Flag                                0x202000a0   Data           1  key.o(.bss.KEYL_Flag)
    KEYL_Times                               0x202000a4   Data           4  key.o(.bss.KEYL_Times)
    KEYM_Flag                                0x202000a8   Data           1  key.o(.bss.KEYM_Flag)
    KEYM_Times                               0x202000ac   Data           4  key.o(.bss.KEYM_Times)
    KEYR_Flag                                0x202000b0   Data           1  key.o(.bss.KEYR_Flag)
    KEYR_Times                               0x202000b4   Data           4  key.o(.bss.KEYR_Times)
    KEYU_Flag                                0x202000b8   Data           1  key.o(.bss.KEYU_Flag)
    KEYU_Times                               0x202000bc   Data           4  key.o(.bss.KEYU_Times)
    LED_Flag                                 0x202000c0   Data           1  main.o(.bss.LED_Flag)
    Last_Error                               0x202000c4   Data           4  user_parameter.o(.bss.Last_Error)
    Line_Out_Flag                            0x202000c8   Data           1  user_control.o(.bss.Line_Out_Flag)
    OLEDLCD_Buffer                           0x202000cc   Data        1024  oled_user.o(.bss.OLEDLCD_Buffer)
    PID_D                                    0x202004cc   Data           4  user_parameter.o(.bss.PID_D)
    PID_I                                    0x202004d0   Data           4  user_parameter.o(.bss.PID_I)
    PID_Out                                  0x202004d4   Data           4  user_parameter.o(.bss.PID_Out)
    Sum_Error                                0x202004d8   Data           4  user_parameter.o(.bss.Sum_Error)
    SysTime_Stick                            0x202004dc   Data           4  user_control.o(.bss.SysTime_Stick)
    gCAPTURE_0Backup                         0x202004e4   Data         120  ti_msp_dl_config.o(.bss.gCAPTURE_0Backup)
    gLCD_SPIBackup                           0x2020055c   Data          40  ti_msp_dl_config.o(.bss.gLCD_SPIBackup)
    gMOTOR_PWMBackup                         0x20200584   Data         188  ti_msp_dl_config.o(.bss.gMOTOR_PWMBackup)
    gQEI_0Backup                             0x20200640   Data         120  ti_msp_dl_config.o(.bss.gQEI_0Backup)
    gSERVO2_PWMBackup                        0x202006b8   Data         188  ti_msp_dl_config.o(.bss.gSERVO2_PWMBackup)
    i                                        0x20200774   Data           4  counting.o(.bss.i)
    j                                        0x20200778   Data           4  counting.o(.bss.j)
    rxData                                   0x20200788   Data           1  uart.o(.bss.rxData)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000036c0, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000036a0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO          132    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          638  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO          817    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO          821    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO          818    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO          823    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO          678    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000166   0x00000166   0x00000000   Code   RO          692    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          694    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          696    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          699    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          701    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          703    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          706    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          708    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          710    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          712    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          714    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          716    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          718    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          720    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          722    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          724    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          726    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          730    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          732    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          734    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          736    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000002   Code   RO          737    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000168   0x00000168   0x00000002   Code   RO          772    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO          800    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO          802    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO          805    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO          808    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO          810    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000000   Code   RO          813    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000016a   0x0000016a   0x00000002   Code   RO          814    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000016c   0x0000016c   0x00000000   Code   RO          640    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000016c   0x0000016c   0x00000000   Code   RO          648    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000016c   0x0000016c   0x00000006   Code   RO          660    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000172   0x00000172   0x00000000   Code   RO          650    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000172   0x00000172   0x00000004   Code   RO          651    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          653    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000176   0x00000176   0x00000008   Code   RO          654    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000017e   0x0000017e   0x00000002   Code   RO          683    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000180   0x00000180   0x00000000   Code   RO          743    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000180   0x00000180   0x00000004   Code   RO          744    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000184   0x00000184   0x00000006   Code   RO          745    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000018a   0x0000018a   0x00000002   PAD
    0x0000018c   0x0000018c   0x0000002c   Code   RO          133    .text               startup_mspm0g350x_uvision.o
    0x000001b8   0x000001b8   0x00000012   Code   RO          624    .text               c_p.l(aeabi_memset.o)
    0x000001ca   0x000001ca   0x00000002   PAD
    0x000001cc   0x000001cc   0x000001f8   Code   RO          628    .text               c_p.l(aeabi_sdivfast.o)
    0x000003c4   0x000003c4   0x00000006   Code   RO          636    .text               c_p.l(heapauxi.o)
    0x000003ca   0x000003ca   0x00000040   Code   RO          645    .text               c_p.l(rt_memclr.o)
    0x0000040a   0x0000040a   0x0000003e   Code   RO          664    .text               c_p.l(sys_stackheap_outer.o)
    0x00000448   0x00000448   0x00000010   Code   RO          667    .text               c_p.l(exit.o)
    0x00000458   0x00000458   0x00000008   Code   RO          679    .text               c_p.l(libspace.o)
    0x00000460   0x00000460   0x0000000c   Code   RO          738    .text               c_p.l(sys_exit.o)
    0x0000046c   0x0000046c   0x00000002   Code   RO          761    .text               c_p.l(use_no_semi.o)
    0x0000046e   0x0000046e   0x00000000   Code   RO          763    .text               c_p.l(indicate_semi.o)
    0x0000046e   0x0000046e   0x00000002   PAD
    0x00000470   0x00000470   0x00000054   Code   RO           17    .text.ADC0_IRQHandler  user_ir_sensor.o
    0x000004c4   0x000004c4   0x0000003c   Code   RO           19    .text.ADC1_IRQHandler  user_ir_sensor.o
    0x00000500   0x00000500   0x00000010   Code   RO           21    .text.ADC_Init      user_ir_sensor.o
    0x00000510   0x00000510   0x0000043c   Code   RO           31    .text.ADC_Transform  user_ir_sensor.o
    0x0000094c   0x0000094c   0x00000020   Code   RO           23    .text.ADC_start     user_ir_sensor.o
    0x0000096c   0x0000096c   0x000000cc   Code   RO           27    .text.Binarization_Display  user_ir_sensor.o
    0x00000a38   0x00000a38   0x000001dc   Code   RO          236    .text.Check_Key     key.o
    0x00000c14   0x00000c14   0x000000d0   Code   RO           53    .text.Control       user_control.o
    0x00000ce4   0x00000ce4   0x00000040   Code   RO          345    .text.DL_ADC12_setClockConfig  driverlib.a(dl_adc12.o)
    0x00000d24   0x00000d24   0x0000000a   Code   RO          357    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00000d2e   0x00000d2e   0x00000026   Code   RO          366    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00000d54   0x00000d54   0x00000044   Code   RO          398    .text.DL_SPI_init   driverlib.a(dl_spi.o)
    0x00000d98   0x00000d98   0x00000012   Code   RO          400    .text.DL_SPI_setClockConfig  driverlib.a(dl_spi.o)
    0x00000daa   0x00000daa   0x00000002   PAD
    0x00000dac   0x00000dac   0x000000c4   Code   RO          591    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000e70   0x00000e70   0x00000028   Code   RO          599    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000e98   0x00000e98   0x00000074   Code   RO          530    .text.DL_TimerA_initPWMMode  driverlib.a(dl_timer.o)
    0x00000f0c   0x00000f0c   0x00000010   Code   RO          482    .text.DL_Timer_getCaptureCompareCtl  driverlib.a(dl_timer.o)
    0x00000f1c   0x00000f1c   0x00000100   Code   RO          462    .text.DL_Timer_initCaptureMode  driverlib.a(dl_timer.o)
    0x0000101c   0x0000101c   0x000000c0   Code   RO          474    .text.DL_Timer_initPWMMode  driverlib.a(dl_timer.o)
    0x000010dc   0x000010dc   0x000000e8   Code   RO          456    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x000011c4   0x000011c4   0x00000018   Code   RO          504    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x000011dc   0x000011dc   0x00000028   Code   RO          460    .text.DL_Timer_setCaptureCompareCtl  driverlib.a(dl_timer.o)
    0x00001204   0x00001204   0x0000001a   Code   RO          464    .text.DL_Timer_setCaptureCompareInput  driverlib.a(dl_timer.o)
    0x0000121e   0x0000121e   0x00000002   PAD
    0x00001220   0x00001220   0x00000018   Code   RO          478    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00001238   0x00001238   0x00000010   Code   RO          458    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00001248   0x00001248   0x0000001c   Code   RO          452    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00001264   0x00001264   0x00000048   Code   RO          551    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x000012ac   0x000012ac   0x00000014   Code   RO          563    .text.DL_UART_receiveDataBlocking  driverlib.a(dl_uart.o)
    0x000012c0   0x000012c0   0x00000012   Code   RO          553    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x000012d2   0x000012d2   0x00000002   PAD
    0x000012d4   0x000012d4   0x00000050   Code   RO           25    .text.Display_ADC   user_ir_sensor.o
    0x00001324   0x00001324   0x00000084   Code   RO           89    .text.Display_Counting_Info  counting.o
    0x000013a8   0x000013a8   0x00000210   Code   RO          307    .text.OLEDLCD_Put12x12CNstr  oled_user.o
    0x000015b8   0x000015b8   0x000000c4   Code   RO          311    .text.OLEDLCD_Put6x12Num  oled_user.o
    0x0000167c   0x0000167c   0x000000fc   Code   RO          305    .text.OLEDLCD_Put6x7Char  oled_user.o
    0x00001778   0x00001778   0x0000002e   Code   RO          309    .text.OLEDLCD_Put6x7ENstr  oled_user.o
    0x000017a6   0x000017a6   0x000000de   Code   RO          313    .text.OLEDLCD_Put6x7Num  oled_user.o
    0x00001884   0x00001884   0x00000040   Code   RO          297    .text.OLEDLCD_Refresh_AllGDRAM  oled_user.o
    0x000018c4   0x000018c4   0x00000074   Code   RO          283    .text.OLED_Fill     oled.o
    0x00001938   0x00001938   0x0000031c   Code   RO          279    .text.OLED_Init     oled.o
    0x00001c54   0x00001c54   0x00000028   Code   RO          281    .text.OLED_WriteCmd  oled.o
    0x00001c7c   0x00001c7c   0x00000010   Code   RO          287    .text.OLED_WriteDat  oled.o
    0x00001c8c   0x00001c8c   0x0000008c   Code   RO          170    .text.SYSCFG_DL_ADC12_0_init  ti_msp_dl_config.o
    0x00001d18   0x00001d18   0x00000084   Code   RO          172    .text.SYSCFG_DL_ADC12_1_init  ti_msp_dl_config.o
    0x00001d9c   0x00001d9c   0x0000002c   Code   RO          156    .text.SYSCFG_DL_CAPTURE_0_init  ti_msp_dl_config.o
    0x00001dc8   0x00001dc8   0x0000012c   Code   RO          144    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001ef4   0x00001ef4   0x0000005c   Code   RO          162    .text.SYSCFG_DL_HC05_UART_init  ti_msp_dl_config.o
    0x00001f50   0x00001f50   0x00000054   Code   RO          160    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00001fa4   0x00001fa4   0x0000005c   Code   RO          168    .text.SYSCFG_DL_LCD_SPI_init  ti_msp_dl_config.o
    0x00002000   0x00002000   0x000000e4   Code   RO          148    .text.SYSCFG_DL_MOTOR_PWM_init  ti_msp_dl_config.o
    0x000020e4   0x000020e4   0x00000002   Code   RO          174    .text.SYSCFG_DL_PWM_Cross_Trigger_init  ti_msp_dl_config.o
    0x000020e6   0x000020e6   0x00000002   PAD
    0x000020e8   0x000020e8   0x0000004c   Code   RO          154    .text.SYSCFG_DL_QEI_0_init  ti_msp_dl_config.o
    0x00002134   0x00002134   0x00000064   Code   RO          164    .text.SYSCFG_DL_SENSER_UART_init  ti_msp_dl_config.o
    0x00002198   0x00002198   0x000000e0   Code   RO          150    .text.SYSCFG_DL_SERVO1_PWM_init  ti_msp_dl_config.o
    0x00002278   0x00002278   0x00000080   Code   RO          152    .text.SYSCFG_DL_SERVO2_PWM_init  ti_msp_dl_config.o
    0x000022f8   0x000022f8   0x00000064   Code   RO          166    .text.SYSCFG_DL_SERVO_UART_init  ti_msp_dl_config.o
    0x0000235c   0x0000235c   0x0000005c   Code   RO          146    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x000023b8   0x000023b8   0x00000038   Code   RO          158    .text.SYSCFG_DL_SYS_TIMER_init  ti_msp_dl_config.o
    0x000023f0   0x000023f0   0x00000078   Code   RO          140    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00002468   0x00002468   0x000000c4   Code   RO          142    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x0000252c   0x0000252c   0x00000090   Code   RO          328    .text.Set_Motor     motor.o
    0x000025bc   0x000025bc   0x00000080   Code   RO            4    .text.TIMG12_IRQHandler  main.o
    0x0000263c   0x0000263c   0x00000040   Code   RO          267    .text.Timer_Start   timer.o
    0x0000267c   0x0000267c   0x0000002c   Code   RO          256    .text.UART0_IRQHandler  uart.o
    0x000026a8   0x000026a8   0x00000084   Code   RO           85    .text.counting      counting.o
    0x0000272c   0x0000272c   0x0000000e   Code   RO          216    .text.delay_ms      delay.o
    0x0000273a   0x0000273a   0x00000002   PAD
    0x0000273c   0x0000273c   0x00000058   Code   RO           87    .text.keycontrol    counting.o
    0x00002794   0x00002794   0x00000090   Code   RO            2    .text.main          main.o
    0x00002824   0x00002824   0x000001f6   Code   RO          629    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x00002a1a   0x00002a1a   0x00000002   PAD
    0x00002a1c   0x00002a1c   0x0000000c   Data   RO          542    .rodata..Lswitch.table.DL_Timer_initCompareMode  driverlib.a(dl_timer.o)
    0x00002a28   0x00002a28   0x000001e0   Data   RO          315    .rodata.ASCII5x7    oled_user.o
    0x00002c08   0x00002c08   0x00000480   Data   RO          319    .rodata.ASCII6X12   oled_user.o
    0x00003088   0x00003088   0x00000008   Data   RO          206    .rodata.gADC12_0ClockConfig  ti_msp_dl_config.o
    0x00003090   0x00003090   0x00000008   Data   RO          207    .rodata.gADC12_1ClockConfig  ti_msp_dl_config.o
    0x00003098   0x00003098   0x00000010   Data   RO          194    .rodata.gCAPTURE_0CaptureConfig  ti_msp_dl_config.o
    0x000030a8   0x000030a8   0x00000003   Data   RO          193    .rodata.gCAPTURE_0ClockConfig  ti_msp_dl_config.o
    0x000030ab   0x000030ab   0x00000002   Data   RO          198    .rodata.gHC05_UARTClockConfig  ti_msp_dl_config.o
    0x000030ad   0x000030ad   0x00000001   PAD
    0x000030ae   0x000030ae   0x0000000a   Data   RO          199    .rodata.gHC05_UARTConfig  ti_msp_dl_config.o
    0x000030b8   0x000030b8   0x00000002   Data   RO          197    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x000030ba   0x000030ba   0x00000002   Data   RO          204    .rodata.gLCD_SPI_clockConfig  ti_msp_dl_config.o
    0x000030bc   0x000030bc   0x0000000a   Data   RO          205    .rodata.gLCD_SPI_config  ti_msp_dl_config.o
    0x000030c6   0x000030c6   0x00000003   Data   RO          186    .rodata.gMOTOR_PWMClockConfig  ti_msp_dl_config.o
    0x000030c9   0x000030c9   0x00000003   PAD
    0x000030cc   0x000030cc   0x00000008   Data   RO          187    .rodata.gMOTOR_PWMConfig  ti_msp_dl_config.o
    0x000030d4   0x000030d4   0x00000003   Data   RO          192    .rodata.gQEI_0ClockConfig  ti_msp_dl_config.o
    0x000030d7   0x000030d7   0x00000002   Data   RO          200    .rodata.gSENSER_UARTClockConfig  ti_msp_dl_config.o
    0x000030d9   0x000030d9   0x00000001   PAD
    0x000030da   0x000030da   0x0000000a   Data   RO          201    .rodata.gSENSER_UARTConfig  ti_msp_dl_config.o
    0x000030e4   0x000030e4   0x00000003   Data   RO          188    .rodata.gSERVO1_PWMClockConfig  ti_msp_dl_config.o
    0x000030e7   0x000030e7   0x00000001   PAD
    0x000030e8   0x000030e8   0x00000008   Data   RO          189    .rodata.gSERVO1_PWMConfig  ti_msp_dl_config.o
    0x000030f0   0x000030f0   0x00000003   Data   RO          190    .rodata.gSERVO2_PWMClockConfig  ti_msp_dl_config.o
    0x000030f3   0x000030f3   0x00000001   PAD
    0x000030f4   0x000030f4   0x00000008   Data   RO          191    .rodata.gSERVO2_PWMConfig  ti_msp_dl_config.o
    0x000030fc   0x000030fc   0x00000002   Data   RO          202    .rodata.gSERVO_UARTClockConfig  ti_msp_dl_config.o
    0x000030fe   0x000030fe   0x0000000a   Data   RO          203    .rodata.gSERVO_UARTConfig  ti_msp_dl_config.o
    0x00003108   0x00003108   0x00000028   Data   RO          185    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00003130   0x00003130   0x00000003   Data   RO          195    .rodata.gSYS_TIMERClockConfig  ti_msp_dl_config.o
    0x00003133   0x00003133   0x00000001   PAD
    0x00003134   0x00003134   0x00000014   Data   RO          196    .rodata.gSYS_TIMERTimerConfig  ti_msp_dl_config.o
    0x00003148   0x00003148   0x0000052e   Data   RO          318    .rodata.hanzi_12x12  oled_user.o
    0x00003676   0x00003676   0x00000004   Data   RO           44    .rodata.str1.1      user_ir_sensor.o
    0x0000367a   0x0000367a   0x00000006   PAD
    0x00003680   0x00003680   0x00000020   Data   RO          816    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000036a0, Size: 0x00000890, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x000036a0   0x0000000c   Data   RW           40    .data.ADC_Threshold  user_ir_sensor.o
    0x2020000c   0x000036ac   0x00000004   Data   RW           57    .data.Line_Out_timesStik  user_control.o
    0x20200010   0x000036b0   0x00000001   Data   RW            6    .data.Mode          main.o
    0x20200011   0x000036b1   0x00000003   PAD
    0x20200014   0x000036b4   0x00000004   Data   RW           74    .data.PID_P         user_parameter.o
    0x20200018   0x000036b8   0x00000004   Data   RW           77    .data.Speed         user_parameter.o
    0x2020001c   0x000036bc   0x00000004   Data   RW           94    .data.Target_Laps   counting.o
    0x20200020        -       0x00000060   Zero   RW          680    .bss                c_p.l(libspace.o)
    0x20200080        -       0x00000001   Zero   RW           43    .bss.ADC1_Flag      user_ir_sensor.o
    0x20200081        -       0x0000000c   Zero   RW           38    .bss.ADC_Data       user_ir_sensor.o
    0x2020008d        -       0x00000001   Zero   RW           42    .bss.ADC_Flag       user_ir_sensor.o
    0x2020008e   0x000036c0   0x00000002   PAD
    0x20200090        -       0x00000004   Zero   RW           33    .bss.Error          user_ir_sensor.o
    0x20200094        -       0x00000002   Zero   RW           34    .bss.IR_RES         user_ir_sensor.o
    0x20200096        -       0x00000002   Zero   RW           36    .bss.IR_RES_For_Error  user_ir_sensor.o
    0x20200098        -       0x00000002   Zero   RW           35    .bss.IR_RES_Original  user_ir_sensor.o
    0x2020009a        -       0x00000001   Zero   RW          244    .bss.KEYD_Flag      key.o
    0x2020009b   0x000036c0   0x00000001   PAD
    0x2020009c        -       0x00000004   Zero   RW          239    .bss.KEYD_Times     key.o
    0x202000a0        -       0x00000001   Zero   RW          246    .bss.KEYL_Flag      key.o
    0x202000a1   0x000036c0   0x00000003   PAD
    0x202000a4        -       0x00000004   Zero   RW          241    .bss.KEYL_Times     key.o
    0x202000a8        -       0x00000001   Zero   RW          247    .bss.KEYM_Flag      key.o
    0x202000a9   0x000036c0   0x00000003   PAD
    0x202000ac        -       0x00000004   Zero   RW          242    .bss.KEYM_Times     key.o
    0x202000b0        -       0x00000001   Zero   RW          245    .bss.KEYR_Flag      key.o
    0x202000b1   0x000036c0   0x00000003   PAD
    0x202000b4        -       0x00000004   Zero   RW          240    .bss.KEYR_Times     key.o
    0x202000b8        -       0x00000001   Zero   RW          243    .bss.KEYU_Flag      key.o
    0x202000b9   0x000036c0   0x00000003   PAD
    0x202000bc        -       0x00000004   Zero   RW          238    .bss.KEYU_Times     key.o
    0x202000c0        -       0x00000001   Zero   RW            7    .bss.LED_Flag       main.o
    0x202000c1   0x000036c0   0x00000003   PAD
    0x202000c4        -       0x00000004   Zero   RW           71    .bss.Last_Error     user_parameter.o
    0x202000c8        -       0x00000001   Zero   RW           58    .bss.Line_Out_Flag  user_control.o
    0x202000c9   0x000036c0   0x00000003   PAD
    0x202000cc        -       0x00000400   Zero   RW          317    .bss.OLEDLCD_Buffer  oled_user.o
    0x202004cc        -       0x00000004   Zero   RW           76    .bss.PID_D          user_parameter.o
    0x202004d0        -       0x00000004   Zero   RW           75    .bss.PID_I          user_parameter.o
    0x202004d4        -       0x00000004   Zero   RW           73    .bss.PID_Out        user_parameter.o
    0x202004d8        -       0x00000004   Zero   RW           72    .bss.Sum_Error      user_parameter.o
    0x202004dc        -       0x00000004   Zero   RW           59    .bss.SysTime_Stick  user_control.o
    0x202004e0        -       0x00000004   Zero   RW           96    .bss.delay_counter  counting.o
    0x202004e4        -       0x00000078   Zero   RW          183    .bss.gCAPTURE_0Backup  ti_msp_dl_config.o
    0x2020055c        -       0x00000028   Zero   RW          184    .bss.gLCD_SPIBackup  ti_msp_dl_config.o
    0x20200584        -       0x000000bc   Zero   RW          180    .bss.gMOTOR_PWMBackup  ti_msp_dl_config.o
    0x20200640        -       0x00000078   Zero   RW          182    .bss.gQEI_0Backup   ti_msp_dl_config.o
    0x202006b8        -       0x000000bc   Zero   RW          181    .bss.gSERVO2_PWMBackup  ti_msp_dl_config.o
    0x20200774        -       0x00000004   Zero   RW           92    .bss.i              counting.o
    0x20200778        -       0x00000004   Zero   RW           93    .bss.j              counting.o
    0x2020077c        -       0x00000004   Zero   RW           98    .bss.key_delay_counter  counting.o
    0x20200780        -       0x00000001   Zero   RW           99    .bss.key_pressed    counting.o
    0x20200781   0x000036c0   0x00000003   PAD
    0x20200784        -       0x00000004   Zero   RW           97    .bss.lap_timer      counting.o
    0x20200788        -       0x00000001   Zero   RW          259    .bss.rxData         uart.o
    0x20200789   0x000036c0   0x00000003   PAD
    0x2020078c        -       0x00000004   Zero   RW           95    .bss.total_time_limit  counting.o
    0x20200790        -       0x00000000   Zero   RW          131    HEAP                startup_mspm0g350x_uvision.o
    0x20200790        -       0x00000100   Zero   RW          130    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       352         64          0          4         25       4321   counting.o
        14          0          0          0          0        580   delay.o
       476         44          0          0         25       4192   key.o
       272         40          0          1          1       8263   main.o
       144         12          0          0          0       6180   motor.o
       968         16          0          0          0      17387   oled.o
      1308         20       2958          0       1024       8433   oled_user.o
        44         18        192          0        256        684   startup_mspm0g350x_uvision.o
      2206        404        184          0        656      54670   ti_msp_dl_config.o
        64         20          0          0          0       5049   timer.o
        44         16          0          0          1       3646   uart.o
       208         52          0          4          5        879   user_control.o
      1560        632          4         12         24       8758   user_ir_sensor.o
         0          0          0          8         20        601   user_parameter.o

    ----------------------------------------------------------------------
      7664       <USER>       <GROUP>         32       2064     123643   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0         14          3         27          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         68   aeabi_memset.o
      1006          4          0          0          0        184   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        64          0          0          0          0        108   rt_memclr.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        64          8          0          0          0       4701   dl_adc12.o
        10          0          0          0          0        802   dl_common.o
        38          0          0          0          0       8590   dl_i2c.o
        86          8          0          0          0      13462   dl_spi.o
       236         24          0          0          0      18490   dl_sysctl_mspm0g1x0x_g3x0x.o
       970        208         12          0          0      40185   dl_timer.o
       110         12          0          0          0      14128   dl_uart.o

    ----------------------------------------------------------------------
      2924        <USER>         <GROUP>          0         96     101266   Library Totals
        30          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1380         22          0          0         96        908   c_p.l
      1514        260         12          0          0     100358   driverlib.a

    ----------------------------------------------------------------------
      2924        <USER>         <GROUP>          0         96     101266   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10588       1624       3396         32       2160     224333   Grand Totals
     10588       1624       3396         32       2160     224333   ELF Image Totals
     10588       1624       3396         32          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13984 (  13.66kB)
    Total RW  Size (RW Data + ZI Data)              2192 (   2.14kB)
    Total ROM Size (Code + RO Data + RW Data)      14016 (  13.69kB)

==============================================================================

