Dependencies for Project 'empty_non_sysconfig_LP_MSPM0G3507_nortos_keil', Target 'empty_non_sysconfig_LP_MSPM0G3507_nortos_keil': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (.\USER\main.c)(0x688AD1E8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/main.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\LED\LED.h)(0x66A7C288)
I (HARDWARE\KEY\key.h)(0x66A7B6F6)
I (HARDWARE\UART\uart.h)(0x6674DB7E)
I (HARDWARE\Timer\timer.h)(0x6674DB7E)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (HARDWARE\OLED\oled_user.h)(0x66A7B272)
I (USER\User_IR_Sensor.h)(0x688AEFEC)
I (HARDWARE\MOTOR\Motor.h)(0x688A03F0)
I (USER\User_Control.h)(0x66A7B75C)
I (USER\User_Parameter.h)(0x66A7D17C)
I (USER\counting.h)(0x688AB355)
F (.\USER\User_IR_Sensor.c)(0x688AF2CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/user_ir_sensor.o -MMD)
I (USER\User_IR_Sensor.h)(0x688AEFEC)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\OLED_User.h)(0x66A7B272)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\MOTOR\Motor.h)(0x688A03F0)
I (HARDWARE\LED\led.h)(0x66A7C288)
F (.\USER\User_Control.c)(0x688AE2B5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/user_control.o -MMD)
I (USER\User_Control.h)(0x66A7B75C)
I (HARDWARE\OLED\OLED_User.h)(0x66A7B272)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\KEY\KEY.h)(0x66A7B6F6)
I (HARDWARE\LED\LED.h)(0x66A7C288)
I (HARDWARE\MOTOR\Motor.h)(0x688A03F0)
I (USER\User_IR_Sensor.h)(0x688AEFEC)
I (USER\User_Parameter.h)(0x66A7D17C)
F (.\USER\User_Parameter.c)(0x688ADB16)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/user_parameter.o -MMD)
I (USER\User_Parameter.h)(0x66A7D17C)
I (HARDWARE\OLED\OLED_User.h)(0x66A7B272)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\KEY\KEY.h)(0x66A7B6F6)
I (HARDWARE\LED\LED.h)(0x66A7C288)
I (HARDWARE\MOTOR\Motor.h)(0x688A03F0)
I (USER\User_IR_Sensor.h)(0x688AEFEC)
I (USER\User_Control.h)(0x66A7B75C)
F (.\USER\counting.c)(0x688AF33F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/counting.o -MMD)
I (USER\User_Control.h)(0x66A7B75C)
I (HARDWARE\OLED\OLED_User.h)(0x66A7B272)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\KEY\KEY.h)(0x66A7B6F6)
I (HARDWARE\LED\LED.h)(0x66A7C288)
I (HARDWARE\MOTOR\Motor.h)(0x688A03F0)
I (USER\User_IR_Sensor.h)(0x688AEFEC)
I (USER\User_Parameter.h)(0x66A7D17C)
I (USER\counting.h)(0x688AB355)
F (.\USER\counting.h)(0x688AB355)()
F (.\USER\UI_Main.c)(0x688AB63E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ui_main.o -MMD)
I (USER\UI_Main.h)(0x66A7B4F6)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\OLED_User.h)(0x66A7B272)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\KEY\KEY.h)(0x66A7B6F6)
I (HARDWARE\LED\LED.h)(0x66A7C288)
I (HARDWARE\MOTOR\Motor.h)(0x688A03F0)
I (USER\User_IR_Sensor.h)(0x688AEFEC)
I (USER\User_Parameter.h)(0x66A7D17C)
I (USER\User_Control.h)(0x66A7B75C)
F (.\USER\UI_Main.h)(0x66A7B4F6)()
F (.\USER\UI_Menu.c)(0x66A7BD3E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ui_menu.o -MMD)
I (USER\UI_Menu.h)(0x66A7B4F6)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\OLED_User.h)(0x66A7B272)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\KEY\KEY.h)(0x66A7B6F6)
I (HARDWARE\LED\LED.h)(0x66A7C288)
I (HARDWARE\MOTOR\Motor.h)(0x688A03F0)
I (USER\User_IR_Sensor.h)(0x688AEFEC)
I (USER\User_Parameter.h)(0x66A7D17C)
I (USER\User_Control.h)(0x66A7B75C)
F (.\USER\UI_Menu.h)(0x66A7B4F6)()
F (.\SYSTEM\startup_mspm0g350x_uvision.s)(0x66631CE8)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-Wa,armasm,--pd,"__UVISION_VERSION SETA 541" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (.\SYSTEM\ti_msp_dl_config.c)(0x6889CC14)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
F (.\SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)()
F (.\SYSTEM\delay.c)(0x669146EE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/delay.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
F (.\HARDWARE\LED\LED.c)(0x66A7A50C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/led.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\LED\LED.h)(0x66A7C288)
I (SYSTEM\delay.h)(0x6674E28E)
F (.\HARDWARE\KEY\KEY.c)(0x688AD20F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\KEY\key.h)(0x66A7B6F6)
I (HARDWARE\LED\led.h)(0x66A7C288)
F (.\HARDWARE\UART\UART.c)(0x66A7A09A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/uart.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\UART\uart.h)(0x6674DB7E)
F (.\HARDWARE\Timer\Timer.c)(0x66A7A306)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/timer.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\Timer\timer.h)(0x6674DB7E)
I (HARDWARE\LED\LED.h)(0x66A7C288)
F (.\HARDWARE\OLED\oled.c)(0x66A7A118)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
F (.\HARDWARE\OLED\oled_user.c)(0x66A7CFC8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled_user.o -MMD)
I (HARDWARE\OLED\OLED_user.h)(0x66A7B272)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\OLED\oled.h)(0x66A79E8A)
I (SYSTEM\delay.h)(0x6674E28E)
I (HARDWARE\OLED\Bitmap.h)(0x5E93E760)
I (HARDWARE\OLED\cn_code.h)(0x6878B046)
I (HARDWARE\OLED\en_code.h)(0x5E93E760)
I (HARDWARE\OLED\Bitmap_shilubi.h)(0x5FBFAD98)
F (.\HARDWARE\MOTOR\MOTOR.c)(0x6889E2A4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
I (HARDWARE\MOTOR\motor.h)(0x688A03F0)
F (.\HARDWARE\buzzer.c)(0x688A75A3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../../../../../../source/third_party/CMSIS/Core/Include -I ../../../../../../source -I ./SYSTEM/ti/devices/msp -I ./SYSTEM/ti/driverlib -I ./SYSTEM/ti -I ./SYSTEM/ti/devices -I ./SYSTEM/ti/devices/msp/m0p -I ./SYSTEM/third_party/CMSIS/Core/Include -I ./SYSTEM/ti/devices/msp/peripherals -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/devices/msp/peripherals/m0p/sysctl -I ./SYSTEM/ti/driverlib/m0p -I ./SYSTEM/ti/driverlib/m0p/sysctl -I ./SYSTEM/ti/devices/msp/peripherals/m0p -I ./SYSTEM/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x -I ./SYSTEM -I ./HARDWARE/LED -I ./HARDWARE/KEY -I ./HARDWARE/UART -I ./HARDWARE/Timer -I ./HARDWARE/OLED -I ./HARDWARE/IMU_DMP -I ./HARDWARE/MOTOR -I ./USER -Wno-invalid-source-encoding

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/buzzer.o -MMD)
I (SYSTEM\ti_msp_dl_config.h)(0x6889CA4C)
I (SYSTEM\ti\devices\msp\msp.h)(0x66723340)
I (SYSTEM\ti\devices\DeviceFamily.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\m0p\mspm0g350x.h)(0x667233FC)
I (SYSTEM\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_adc12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_aes.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_comp.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_crc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dac12.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_dma.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_flashctl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gpio.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_gptimer.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_i2c.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_iomux.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mathacl.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_mcan.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_oa.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_rtc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_spi.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_trng.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_uart.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_vref.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wuc.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\hw_wwdt.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66631CE6)
I (SYSTEM\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66723434)
I (SYSTEM\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66631CE6)
I (SYSTEM\ti\driverlib\driverlib.h)(0x66723496)
I (SYSTEM\ti\driverlib\dl_adc12.h)(0x667234A6)
I (SYSTEM\ti\driverlib\dl_common.h)(0x667234B0)
I (SYSTEM\ti\driverlib\m0p\dl_factoryregion.h)(0x66723522)
I (SYSTEM\ti\driverlib\m0p\dl_core.h)(0x66723530)
I (SYSTEM\ti\driverlib\dl_aes.h)(0x6672353C)
I (SYSTEM\ti\driverlib\dl_aesadv.h)(0x66723546)
I (SYSTEM\ti\driverlib\dl_comp.h)(0x66723550)
I (SYSTEM\ti\driverlib\dl_crc.h)(0x6672355A)
I (SYSTEM\ti\driverlib\dl_crcp.h)(0x66723562)
I (SYSTEM\ti\driverlib\dl_dac12.h)(0x6672356E)
I (SYSTEM\ti\driverlib\dl_dma.h)(0x66723574)
I (SYSTEM\ti\driverlib\dl_flashctl.h)(0x66723584)
I (SYSTEM\ti\driverlib\m0p\dl_sysctl.h)(0x667235C0)
I (SYSTEM\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x667235CC)
I (SYSTEM\ti\driverlib\dl_gpamp.h)(0x667235D4)
I (SYSTEM\ti\driverlib\dl_gpio.h)(0x667235D8)
I (SYSTEM\ti\driverlib\dl_i2c.h)(0x667235DE)
I (SYSTEM\ti\driverlib\dl_iwdt.h)(0x667235E6)
I (SYSTEM\ti\driverlib\dl_lfss.h)(0x667235EE)
I (SYSTEM\ti\driverlib\dl_keystorectl.h)(0x667235F4)
I (SYSTEM\ti\driverlib\dl_lcd.h)(0x667235FA)
I (SYSTEM\ti\driverlib\dl_mathacl.h)(0x667235FE)
I (SYSTEM\ti\driverlib\dl_mcan.h)(0x66723606)
I (SYSTEM\ti\driverlib\dl_opa.h)(0x6672360C)
I (SYSTEM\ti\driverlib\dl_rtc.h)(0x66723614)
I (SYSTEM\ti\driverlib\dl_rtc_common.h)(0x6672361A)
I (SYSTEM\ti\driverlib\dl_rtc_a.h)(0x66723620)
I (SYSTEM\ti\driverlib\dl_scratchpad.h)(0x66723628)
I (SYSTEM\ti\driverlib\dl_spi.h)(0x6672362E)
I (SYSTEM\ti\driverlib\dl_tamperio.h)(0x6672363A)
I (SYSTEM\ti\driverlib\dl_timera.h)(0x66723640)
I (SYSTEM\ti\driverlib\dl_timer.h)(0x66723646)
I (SYSTEM\ti\driverlib\dl_timerg.h)(0x6672364C)
I (SYSTEM\ti\driverlib\dl_trng.h)(0x66723652)
I (SYSTEM\ti\driverlib\dl_uart_extend.h)(0x66723656)
I (SYSTEM\ti\driverlib\dl_uart.h)(0x6672365C)
I (SYSTEM\ti\driverlib\dl_uart_main.h)(0x66723662)
I (SYSTEM\ti\driverlib\dl_vref.h)(0x6672367A)
I (SYSTEM\ti\driverlib\dl_wwdt.h)(0x66723680)
I (SYSTEM\ti\driverlib\m0p\dl_interrupt.h)(0x667236A6)
I (SYSTEM\ti\driverlib\m0p\dl_systick.h)(0x667236AC)
F (.\HARDWARE\buzzer.h)(0x688A7570)()
